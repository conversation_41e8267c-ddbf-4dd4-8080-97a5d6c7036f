# -*- coding: utf-8 -*-
"""
مدير المتصفح - للتحكم في متصفح الويب
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import time
from config import BROWSER_CONFIG
from utils.logger import logger


class WebDriverManager:
    """فئة إدارة المتصفح"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.is_initialized = False
    
    def initialize_driver(self, headless=None):
        """تهيئة المتصفح"""
        try:
            # إعداد خيارات Chrome
            chrome_options = Options()
            
            if headless is None:
                headless = BROWSER_CONFIG["headless"]
            
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            
            # تثبيت وإعداد ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # إنشاء المتصفح
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # إعداد الانتظار
            self.driver.implicitly_wait(BROWSER_CONFIG["implicit_wait"])
            self.driver.set_page_load_timeout(BROWSER_CONFIG["page_load_timeout"])
            
            # إعداد حجم النافذة
            if not headless:
                width, height = BROWSER_CONFIG["window_size"]
                self.driver.set_window_size(width, height)
            
            self.wait = WebDriverWait(self.driver, 10)
            self.is_initialized = True
            
            logger.info("تم تهيئة المتصفح بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة المتصفح: {str(e)}")
            return False
    
    def navigate_to(self, url):
        """الانتقال إلى رابط معين"""
        if not self.is_initialized:
            logger.error("المتصفح غير مهيأ")
            return False
        
        try:
            self.driver.get(url)
            logger.info(f"تم الانتقال إلى: {url}")
            return True
        except Exception as e:
            logger.error(f"خطأ في الانتقال إلى {url}: {str(e)}")
            return False
    
    def find_element(self, by, value, timeout=10):
        """البحث عن عنصر في الصفحة"""
        try:
            wait = WebDriverWait(self.driver, timeout)
            element = wait.until(EC.presence_of_element_located((by, value)))
            return element
        except TimeoutException:
            logger.warning(f"لم يتم العثور على العنصر: {by}={value}")
            return None
    
    def click_element(self, element):
        """النقر على عنصر"""
        try:
            # انتظار حتى يصبح العنصر قابلاً للنقر
            WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(element))
            element.click()
            return True
        except Exception as e:
            logger.error(f"خطأ في النقر على العنصر: {str(e)}")
            return False
    
    def send_keys_to_element(self, element, text):
        """إدخال نص في عنصر"""
        try:
            element.clear()
            element.send_keys(text)
            return True
        except Exception as e:
            logger.error(f"خطأ في إدخال النص: {str(e)}")
            return False
    
    def take_screenshot(self, filename=None):
        """أخذ لقطة شاشة"""
        try:
            if filename is None:
                filename = f"screenshot_{int(time.time())}.png"
            
            self.driver.save_screenshot(filename)
            return filename
        except Exception as e:
            logger.error(f"خطأ في أخذ لقطة الشاشة: {str(e)}")
            return None
    
    def get_current_url(self):
        """الحصول على الرابط الحالي"""
        if self.driver:
            return self.driver.current_url
        return None
    
    def get_page_title(self):
        """الحصول على عنوان الصفحة"""
        if self.driver:
            return self.driver.title
        return None
    
    def close(self):
        """إغلاق المتصفح"""
        if self.driver:
            try:
                self.driver.quit()
                self.is_initialized = False
                logger.info("تم إغلاق المتصفح")
            except Exception as e:
                logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.close()
