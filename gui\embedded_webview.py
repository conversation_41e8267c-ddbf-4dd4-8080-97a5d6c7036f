# -*- coding: utf-8 -*-
"""
متصفح مدمج حقيقي داخل البرنامج باستخدام webview
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
import tempfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from utils.logger import logger

# محاولة استيراد webview
try:
    import webview
    WEBVIEW_AVAILABLE = True
    logger.info("تم تحميل مكتبة webview بنجاح")
except ImportError:
    WEBVIEW_AVAILABLE = False
    logger.warning("مكتبة webview غير متوفرة، سيتم استخدام البديل")


class EmbeddedWebView(ttk.Frame):
    """متصفح مدمج حقيقي داخل البرنامج"""

    def __init__(self, parent, data_manager=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.web_driver = None
        self.current_url = ""
        self.page_elements = []
        self.automation_running = False
        self.temp_html_file = None

        self.create_widgets()

    def create_widgets(self):
        """إنشاء واجهة المتصفح المدمج"""
        # شريط التحكم العلوي
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        # شريط الرابط
        url_frame = ttk.Frame(control_frame)
        url_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(url_frame, text="الرابط:").pack(side=tk.LEFT)

        self.url_var = tk.StringVar(value="https://quickly24erp.com")
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 9))
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.btn_go = ttk.Button(url_frame, text="🌐 انتقال", command=self.navigate_to_url)
        self.btn_go.pack(side=tk.RIGHT)

        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill=tk.X)

        self.btn_refresh = ttk.Button(buttons_frame, text="🔄 تحديث", command=self.refresh_page)
        self.btn_refresh.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_detect = ttk.Button(buttons_frame, text="🔍 كشف العناصر", command=self.detect_elements)
        self.btn_detect.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_auto_fill = ttk.Button(buttons_frame, text="🤖 تعبئة تلقائية", command=self.start_auto_fill)
        self.btn_auto_fill.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop = ttk.Button(buttons_frame, text="⏹️ إيقاف", command=self.stop_automation, state=tk.DISABLED)
        self.btn_stop.pack(side=tk.LEFT, padx=(0, 5))

        # إطار المحتوى الرئيسي
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # الجانب الأيسر - عرض الصفحة المدمج
        left_frame = ttk.LabelFrame(main_frame, text="عرض الصفحة المدمج", padding=5)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # إنشاء عارض الويب المدمج
        if WEBVIEW_AVAILABLE:
            self.create_webview_widget(left_frame)
        else:
            # استخدام iframe مدمج في HTML
            self.create_html_iframe_viewer(left_frame)

        # الجانب الأيمن - العناصر والتحكم
        right_frame = ttk.LabelFrame(main_frame, text="العناصر التفاعلية", padding=5)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.config(width=300)

        # قائمة العناصر
        self.elements_tree = ttk.Treeview(
            right_frame,
            columns=("النوع", "المعرف", "النص"),
            show="headings",
            height=15
        )

        for col in ("النوع", "المعرف", "النص"):
            self.elements_tree.heading(col, text=col)
            self.elements_tree.column(col, width=80)

        self.elements_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.elements_tree.bind('<Double-1>', self.on_element_select)

        # أزرار التحكم في العناصر
        elements_control = ttk.Frame(right_frame)
        elements_control.pack(fill=tk.X)

        self.btn_click_element = ttk.Button(
            elements_control,
            text="👆 نقر على العنصر",
            command=self.click_selected_element
        )
        self.btn_click_element.pack(fill=tk.X, pady=(0, 2))

        self.btn_fill_element = ttk.Button(
            elements_control,
            text="✏️ تعبئة العنصر",
            command=self.fill_selected_element
        )
        self.btn_fill_element.pack(fill=tk.X, pady=(0, 2))

        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_label = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, padx=5, pady=5)

    def create_webview_widget(self, parent):
        """إنشاء عارض ويب حقيقي باستخدام webview"""
        try:
            # إنشاء إطار لاحتواء webview
            self.webview_frame = ttk.Frame(parent)
            self.webview_frame.pack(fill=tk.BOTH, expand=True)

            # إنشاء webview في خيط منفصل
            self.webview_window = None
            logger.info("تم إعداد عارض الويب المدمج")

            # عرض رسالة انتظار
            self.html_widget = tk.Text(
                self.webview_frame,
                wrap=tk.WORD,
                bg="#f8f9fa",
                fg="#6c757d",
                font=("Arial", 12),
                state=tk.DISABLED
            )
            self.html_widget.pack(fill=tk.BOTH, expand=True)

            # عرض رسالة ترحيب
            welcome_text = """
🌐 مرحباً بك في المتصفح المدمج المطور

📋 التعليمات:
1. أدخل رابط الموقع في الحقل أعلاه
2. اضغط على "🌐 انتقال" لتحميل الموقع
3. انتظر تحميل الصفحة كاملة
4. اضغط "🔍 كشف العناصر" لرؤية العناصر التفاعلية
5. استخدم "🤖 تعبئة تلقائية" لبدء التعبئة

✨ الميزات الجديدة:
• عرض الموقع داخل البرنامج مباشرة
• كشف العناصر التفاعلية تلقائياً
• تعبئة ذكية للنماذج
• تفاعل مباشر مع الصفحات

🔧 جاري الإعداد...
            """

            self.html_widget.config(state=tk.NORMAL)
            self.html_widget.insert(tk.END, welcome_text)
            self.html_widget.config(state=tk.DISABLED)

        except Exception as e:
            logger.error(f"خطأ في إنشاء عارض الويب: {str(e)}")
            self.create_html_iframe_viewer(parent)

    def create_html_iframe_viewer(self, parent):
        """إنشاء عارض HTML مع iframe مدمج"""
        try:
            import tkinter.html as tkhtml

            # إنشاء عارض HTML
            self.html_widget = tkhtml.HtmlFrame(parent)
            self.html_widget.pack(fill=tk.BOTH, expand=True)

            # تحميل صفحة HTML أساسية
            initial_html = """
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>المتصفح المدمج</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        text-align: center;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: rgba(255,255,255,0.1);
                        padding: 30px;
                        border-radius: 15px;
                        backdrop-filter: blur(10px);
                    }
                    .title {
                        font-size: 2.5em;
                        margin-bottom: 20px;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    }
                    .subtitle {
                        font-size: 1.2em;
                        margin-bottom: 30px;
                        opacity: 0.9;
                    }
                    .instructions {
                        text-align: right;
                        background: rgba(255,255,255,0.1);
                        padding: 20px;
                        border-radius: 10px;
                        margin: 20px 0;
                    }
                    .step {
                        margin: 10px 0;
                        padding: 10px;
                        background: rgba(255,255,255,0.05);
                        border-radius: 5px;
                    }
                    iframe {
                        width: 100%;
                        height: 600px;
                        border: none;
                        border-radius: 10px;
                        background: white;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="title">🌐 المتصفح المدمج المطور</div>
                    <div class="subtitle">عرض المواقع داخل البرنامج مباشرة</div>

                    <div class="instructions">
                        <h3>📋 كيفية الاستخدام:</h3>
                        <div class="step">1️⃣ أدخل رابط الموقع في الحقل أعلاه</div>
                        <div class="step">2️⃣ اضغط على "🌐 انتقال" لتحميل الموقع</div>
                        <div class="step">3️⃣ انتظر تحميل الصفحة كاملة</div>
                        <div class="step">4️⃣ اضغط "🔍 كشف العناصر" لرؤية العناصر التفاعلية</div>
                        <div class="step">5️⃣ استخدم "🤖 تعبئة تلقائية" لبدء التعبئة</div>
                    </div>

                    <div id="website-container" style="display:none;">
                        <iframe id="website-frame" src="about:blank"></iframe>
                    </div>
                </div>

                <script>
                    function loadWebsite(url) {
                        const container = document.getElementById('website-container');
                        const frame = document.getElementById('website-frame');

                        if (url && url !== 'about:blank') {
                            frame.src = url;
                            container.style.display = 'block';
                            document.querySelector('.instructions').style.display = 'none';
                        }
                    }
                </script>
            </body>
            </html>
            """

            self.html_widget.load_html(initial_html)
            logger.info("تم إنشاء عارض HTML مع iframe")

        except ImportError:
            logger.warning("tkinter.html غير متوفر، استخدام العارض النصي")
            self.create_text_based_viewer(parent)
        except Exception as e:
            logger.error(f"خطأ في إنشاء عارض HTML: {str(e)}")
            self.create_text_based_viewer(parent)

    def create_text_based_viewer(self, parent):
        """إنشاء عارض نصي كبديل"""
        self.html_widget = tk.Text(
            parent,
            wrap=tk.WORD,
            bg="white",
            fg="black",
            font=("Arial", 10),
            state=tk.DISABLED
        )

        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, command=self.html_widget.yview)
        self.html_widget.configure(yscrollcommand=scrollbar.set)

        # تخطيط العرض
        self.html_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        logger.info("تم إنشاء عارض نصي كبديل")

    def navigate_to_url(self):
        """الانتقال إلى رابط معين"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط")
            return

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_var.set(url)

        threading.Thread(target=self._navigate_thread, args=(url,), daemon=True).start()

    def _navigate_thread(self, url):
        """الانتقال في خيط منفصل"""
        try:
            self.status_var.set("جاري تحميل الصفحة...")

            if not self.web_driver:
                self._init_driver()

            # تحميل الصفحة في المتصفح الخفي
            self.web_driver.get(url)
            self.current_url = url

            # الحصول على HTML وعرضه في الواجهة
            page_source = self.web_driver.page_source

            # حفظ HTML في ملف مؤقت وعرضه
            self._display_html_in_widget(page_source, url)

            self.after(0, lambda: self.status_var.set("تم تحميل الصفحة"))

        except Exception as e:
            logger.error(f"خطأ في الانتقال: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ: {str(e)}"))

    def _init_driver(self):
        """تهيئة المتصفح الخفي"""
        try:
            options = Options()
            options.add_argument("--headless")  # تشغيل خفي
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--window-size=1200,800")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")

            service = Service(ChromeDriverManager().install())
            self.web_driver = webdriver.Chrome(service=service, options=options)

            logger.info("تم تهيئة المتصفح الخفي")

        except Exception as e:
            logger.error(f"خطأ في تهيئة المتصفح: {str(e)}")
            raise

    def _display_html_in_widget(self, html_content, url):
        """عرض HTML في الواجهة"""
        try:
            if hasattr(self.html_widget, 'load_html'):
                # إنشاء HTML محسن مع iframe للموقع الفعلي
                enhanced_html = self._create_enhanced_html_with_iframe(url)
                self.html_widget.load_html(enhanced_html)
                logger.info(f"تم عرض الموقع في iframe: {url}")
            elif hasattr(self.html_widget, 'execute_script'):
                # استخدام JavaScript لتحميل الموقع في iframe
                script = f"loadWebsite('{url}');"
                self.html_widget.execute_script(script)
                logger.info(f"تم تحميل الموقع باستخدام JavaScript: {url}")
            else:
                # استخدام Text widget كبديل
                self._display_html_as_text(html_content, url)

        except Exception as e:
            logger.error(f"خطأ في عرض HTML: {str(e)}")
            self._display_html_as_text(html_content, url)

    def _create_enhanced_html_with_iframe(self, url):
        """إنشاء HTML محسن مع iframe للموقع"""
        return f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>عرض الموقع - {url}</title>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #f8f9fa;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 10px;
                    text-align: center;
                    font-size: 14px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .iframe-container {{
                    position: relative;
                    width: 100%;
                    height: calc(100vh - 60px);
                    background: white;
                }}
                iframe {{
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: white;
                }}
                .loading {{
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    color: #6c757d;
                    font-size: 16px;
                }}
                .error {{
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    text-align: center;
                    color: #dc3545;
                    font-size: 16px;
                    display: none;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                🌐 عرض الموقع: {url}
            </div>

            <div class="iframe-container">
                <div class="loading" id="loading">
                    <div>⏳ جاري تحميل الموقع...</div>
                    <div style="font-size: 12px; margin-top: 10px;">يرجى الانتظار</div>
                </div>

                <div class="error" id="error">
                    <div>❌ خطأ في تحميل الموقع</div>
                    <div style="font-size: 12px; margin-top: 10px;">تحقق من الرابط والاتصال</div>
                </div>

                <iframe
                    id="website-frame"
                    src="{url}"
                    onload="hideLoading()"
                    onerror="showError()"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
                ></iframe>
            </div>

            <script>
                function hideLoading() {{
                    document.getElementById('loading').style.display = 'none';
                    console.log('تم تحميل الموقع بنجاح');
                }}

                function showError() {{
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'block';
                    console.log('خطأ في تحميل الموقع');
                }}

                // إخفاء رسالة التحميل بعد 10 ثوان كحد أقصى
                setTimeout(function() {{
                    hideLoading();
                }}, 10000);
            </script>
        </body>
        </html>
        """

    def _display_html_as_text(self, html_content, url):
        """عرض HTML كنص منسق"""
        try:
            # استخراج المحتوى النصي من HTML
            formatted_content = self._extract_readable_content(html_content)

            # عرض المحتوى
            display_text = f"🌐 الموقع: {url}\n"
            display_text += "=" * 80 + "\n\n"
            display_text += formatted_content

            self.html_widget.config(state=tk.NORMAL)
            self.html_widget.delete(1.0, tk.END)
            self.html_widget.insert(tk.END, display_text)
            self.html_widget.config(state=tk.DISABLED)

        except Exception as e:
            logger.error(f"خطأ في عرض النص: {str(e)}")

    def _extract_readable_content(self, html_content):
        """استخراج محتوى قابل للقراءة من HTML"""
        try:
            import re

            # إزالة العناصر غير المرغوبة
            html_content = re.sub(r'<script.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<style.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
            html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)

            # استخراج النصوص من العناصر المهمة
            important_patterns = [
                (r'<title[^>]*>(.*?)</title>', "📋 العنوان: "),
                (r'<h[1-6][^>]*>(.*?)</h[1-6]>', "📌 عنوان: "),
                (r'<p[^>]*>(.*?)</p>', "📄 فقرة: "),
                (r'<button[^>]*>(.*?)</button>', "🔘 زر: "),
                (r'<input[^>]*placeholder=["\']([^"\']*)["\'][^>]*>', "📝 حقل: "),
                (r'<label[^>]*>(.*?)</label>', "🏷️ تسمية: "),
                (r'<a[^>]*>(.*?)</a>', "🔗 رابط: "),
            ]

            extracted_content = []

            for pattern, prefix in important_patterns:
                matches = re.findall(pattern, html_content, flags=re.DOTALL | re.IGNORECASE)
                for match in matches:
                    clean_text = re.sub(r'<[^>]+>', '', match).strip()
                    if clean_text and len(clean_text) > 1:
                        extracted_content.append(f"{prefix}{clean_text}")

            # إزالة التكرارات وأخذ أول 50 عنصر
            unique_content = []
            for item in extracted_content:
                if len(item) < 200 and item not in unique_content:
                    unique_content.append(item)

            if len(unique_content) > 50:
                unique_content = unique_content[:50]
                unique_content.append("... (تم اقتطاع المحتوى)")

            return '\n'.join(unique_content) if unique_content else "لا يوجد محتوى واضح في الصفحة"

        except Exception as e:
            logger.error(f"خطأ في استخراج المحتوى: {str(e)}")
            return "خطأ في استخراج المحتوى"

    def refresh_page(self):
        """تحديث الصفحة"""
        if self.current_url:
            self.navigate_to_url()

    def detect_elements(self):
        """كشف العناصر التفاعلية"""
        if not self.web_driver:
            messagebox.showwarning("تحذير", "يرجى الانتقال لصفحة أولاً")
            return

        threading.Thread(target=self._detect_elements_thread, daemon=True).start()

    def _detect_elements_thread(self):
        """كشف العناصر في خيط منفصل"""
        try:
            self.after(0, lambda: self.status_var.set("جاري كشف العناصر..."))

            # مسح القائمة السابقة
            self.after(0, lambda: self.elements_tree.delete(*self.elements_tree.get_children()))

            self.page_elements = []

            # البحث عن أنواع مختلفة من العناصر
            element_selectors = [
                ("input", "حقل إدخال"),
                ("button", "زر"),
                ("select", "قائمة منسدلة"),
                ("textarea", "منطقة نص"),
                ("a", "رابط"),
                ("[onclick]", "عنصر قابل للنقر"),
                ("[type='submit']", "زر إرسال"),
                ("[type='button']", "زر"),
                ("[role='button']", "زر"),
                (".btn", "زر"),
                (".button", "زر"),
                ("form", "نموذج")
            ]

            from selenium.webdriver.common.by import By

            for selector, element_type in element_selectors:
                try:
                    elements = self.web_driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                element_info = {
                                    "element": element,
                                    "type": element_type,
                                    "tag": element.tag_name,
                                    "id": element.get_attribute("id") or "",
                                    "name": element.get_attribute("name") or "",
                                    "class": element.get_attribute("class") or "",
                                    "text": element.text[:30] if element.text else "",
                                    "placeholder": element.get_attribute("placeholder") or "",
                                    "value": element.get_attribute("value") or "",
                                    "xpath": self._get_element_xpath(element)
                                }

                                self.page_elements.append(element_info)

                        except Exception as e:
                            logger.debug(f"خطأ في معالجة عنصر: {str(e)}")
                            continue

                except Exception as e:
                    logger.debug(f"خطأ في البحث عن {selector}: {str(e)}")
                    continue

            # تحديث القائمة
            self.after(0, self._update_elements_list)
            self.after(0, lambda: self.status_var.set(f"تم العثور على {len(self.page_elements)} عنصر"))

        except Exception as e:
            logger.error(f"خطأ في كشف العناصر: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ في كشف العناصر"))

    def _get_element_xpath(self, element):
        """الحصول على XPath للعنصر"""
        try:
            return self.web_driver.execute_script(
                "function getXPath(element) {"
                "if (element.id !== '') return '//*[@id=\"' + element.id + '\"]';"
                "if (element === document.body) return '/html/body';"
                "var ix = 0;"
                "var siblings = element.parentNode.childNodes;"
                "for (var i = 0; i < siblings.length; i++) {"
                "var sibling = siblings[i];"
                "if (sibling === element) return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';"
                "if (sibling.nodeType === 1 && sibling.tagName === element.tagName) ix++;"
                "}"
                "}"
                "return getXPath(arguments[0]);", element
            )
        except:
            return None

    def _update_elements_list(self):
        """تحديث قائمة العناصر"""
        try:
            for element_info in self.page_elements:
                display_text = element_info["text"] or element_info["placeholder"] or element_info["id"] or "عنصر"

                self.elements_tree.insert("", tk.END, values=(
                    element_info["type"],
                    element_info["id"][:15],
                    display_text[:20]
                ))

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة العناصر: {str(e)}")

    def on_element_select(self, event):
        """عند تحديد عنصر من القائمة"""
        try:
            selection = self.elements_tree.selection()
            if selection:
                item = selection[0]
                index = self.elements_tree.index(item)

                if 0 <= index < len(self.page_elements):
                    element_info = self.page_elements[index]
                    self.status_var.set(f"تم تحديد: {element_info['type']} - {element_info['id']}")

        except Exception as e:
            logger.error(f"خطأ في تحديد العنصر: {str(e)}")

    def click_selected_element(self):
        """النقر على العنصر المحدد"""
        try:
            selection = self.elements_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد عنصر أولاً")
                return

            item = selection[0]
            index = self.elements_tree.index(item)

            if 0 <= index < len(self.page_elements):
                element_info = self.page_elements[index]
                element = element_info["element"]

                # النقر على العنصر
                threading.Thread(target=self._click_element_thread, args=(element,), daemon=True).start()

        except Exception as e:
            logger.error(f"خطأ في النقر على العنصر: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في النقر على العنصر: {str(e)}")

    def _click_element_thread(self, element):
        """النقر على العنصر في خيط منفصل"""
        try:
            import time
            self.after(0, lambda: self.status_var.set("جاري النقر على العنصر..."))

            # التمرير للعنصر
            self.web_driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # النقر
            element.click()

            self.after(0, lambda: self.status_var.set("تم النقر على العنصر"))

            # تحديث العرض
            time.sleep(1)
            page_source = self.web_driver.page_source
            self.after(0, lambda: self._display_html_in_widget(page_source, self.current_url))

        except Exception as e:
            logger.error(f"خطأ في النقر: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ في النقر: {str(e)}"))

    def fill_selected_element(self):
        """تعبئة العنصر المحدد"""
        try:
            selection = self.elements_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد عنصر أولاً")
                return

            if not self.data_manager or not self.data_manager.is_data_loaded():
                messagebox.showwarning("تحذير", "يرجى تحميل البيانات أولاً")
                return

            item = selection[0]
            index = self.elements_tree.index(item)

            if 0 <= index < len(self.page_elements):
                element_info = self.page_elements[index]

                # الحصول على بيانات للتعبئة
                row_data = self.data_manager.get_current_row_data()
                if row_data:
                    field_name = self._identify_field_type(element_info)
                    if field_name and field_name in row_data:
                        value = str(row_data[field_name])
                        threading.Thread(target=self._fill_element_thread, args=(element_info["element"], value), daemon=True).start()
                    else:
                        messagebox.showinfo("معلومات", "لم يتم العثور على بيانات مناسبة لهذا العنصر")

        except Exception as e:
            logger.error(f"خطأ في تعبئة العنصر: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تعبئة العنصر: {str(e)}")

    def _fill_element_thread(self, element, value):
        """تعبئة العنصر في خيط منفصل"""
        try:
            import time
            self.after(0, lambda: self.status_var.set("جاري تعبئة العنصر..."))

            # التمرير للعنصر
            self.web_driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # تنظيف الحقل وإدخال القيمة
            element.clear()
            element.send_keys(value)

            self.after(0, lambda: self.status_var.set(f"تم تعبئة العنصر بالقيمة: {value}"))

        except Exception as e:
            logger.error(f"خطأ في تعبئة العنصر: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ في التعبئة: {str(e)}"))

    def _identify_field_type(self, element_info):
        """تحديد نوع الحقل من معلوماته"""
        if not self.data_manager:
            return None

        # البحث في الخصائص المختلفة
        search_terms = [
            element_info.get("name", "").lower(),
            element_info.get("id", "").lower(),
            element_info.get("placeholder", "").lower(),
            element_info.get("class", "").lower()
        ]

        # مطابقة مع أسماء الأعمدة
        for column_name in self.data_manager.get_column_names():
            column_lower = column_name.lower()

            for term in search_terms:
                if term and (column_lower in term or term in column_lower):
                    return column_name

        return None

    def start_auto_fill(self):
        """بدء التعبئة التلقائية"""
        if not self.data_manager or not self.data_manager.is_data_loaded():
            messagebox.showwarning("تحذير", "يرجى تحميل البيانات أولاً")
            return

        if not self.page_elements:
            messagebox.showwarning("تحذير", "يرجى كشف العناصر أولاً")
            return

        self.automation_running = True
        self.btn_auto_fill.config(state=tk.DISABLED)
        self.btn_stop.config(state=tk.NORMAL)

        threading.Thread(target=self._auto_fill_thread, daemon=True).start()

    def _auto_fill_thread(self):
        """خيط التعبئة التلقائية"""
        try:
            self.data_manager.reset_position()

            while self.automation_running and self.data_manager.get_remaining_rows() > 0:
                # الحصول على بيانات الصف الحالي
                row_data = self.data_manager.get_current_row_data()
                if not row_data:
                    break

                self.after(0, lambda: self.status_var.set(f"تعبئة الصف {self.data_manager.current_row + 1}"))

                # تعبئة الحقول
                success = self._fill_form_fields(row_data)

                if success:
                    # الانتقال للصف التالي
                    self.data_manager.get_next_row_data()
                    import time
                    time.sleep(2)  # تأخير بين الصفوف
                else:
                    logger.warning("فشل في تعبئة الصف")
                    break

            self.after(0, self._auto_fill_finished)

        except Exception as e:
            logger.error(f"خطأ في التعبئة التلقائية: {str(e)}")
            self.after(0, self._auto_fill_finished)

    def _fill_form_fields(self, row_data):
        """تعبئة حقول النموذج"""
        try:
            import time
            # البحث عن حقول الإدخال وتعبئتها
            for element_info in self.page_elements:
                if element_info["type"] in ["حقل إدخال", "منطقة نص"]:
                    element = element_info["element"]

                    # محاولة تحديد نوع الحقل من اسمه أو معرفه
                    field_name = self._identify_field_type(element_info)

                    if field_name and field_name in row_data:
                        try:
                            # تنظيف الحقل وإدخال القيمة
                            element.clear()
                            element.send_keys(str(row_data[field_name]))
                            time.sleep(0.5)

                        except Exception as e:
                            logger.debug(f"خطأ في تعبئة الحقل {field_name}: {str(e)}")
                            continue

            return True

        except Exception as e:
            logger.error(f"خطأ في تعبئة النموذج: {str(e)}")
            return False

    def _auto_fill_finished(self):
        """عند انتهاء التعبئة التلقائية"""
        self.automation_running = False
        self.btn_auto_fill.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.status_var.set("تم إكمال التعبئة التلقائية")
        messagebox.showinfo("مكتمل", "تم إكمال التعبئة التلقائية")

    def stop_automation(self):
        """إيقاف التعبئة التلقائية"""
        self.automation_running = False
        self.btn_auto_fill.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.status_var.set("تم إيقاف التعبئة التلقائية")

    def close_browser(self):
        """إغلاق المتصفح"""
        try:
            if self.web_driver:
                self.web_driver.quit()
                self.web_driver = None
                logger.info("تم إغلاق المتصفح المدمج")
        except Exception as e:
            logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")