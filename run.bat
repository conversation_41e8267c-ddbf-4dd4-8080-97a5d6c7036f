@echo off
chcp 65001 > nul
title برنامج تعبئة البيانات التلقائي

echo ========================================
echo برنامج تعبئة البيانات التلقائي
echo ========================================
echo.

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.7 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo جاري التحقق من المتطلبات...
pip show selenium > nul 2>&1
if errorlevel 1 (
    echo جاري تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo جاري تشغيل البرنامج...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    pause
)
