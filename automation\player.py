# -*- coding: utf-8 -*-
"""
مشغل الإجراءات - لتشغيل الإجراءات المسجلة تلقائياً
"""

import json
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from config import RECORDING_CONFIG, SESSIONS_DIR
from utils.logger import logger


class ActionPlayer:
    """فئة تشغيل الإجراءات"""
    
    def __init__(self, web_driver):
        self.web_driver = web_driver
        self.loaded_actions = []
        self.current_action_index = 0
        self.is_playing = False
        self.data_mapping = {}
        
    def load_recording(self, file_path):
        """تحميل التسجيل من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                recording_data = json.load(f)
            
            self.loaded_actions = recording_data.get("actions", [])
            self.current_action_index = 0
            
            logger.info(f"تم تحميل التسجيل: {len(self.loaded_actions)} إجراء")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل التسجيل: {str(e)}")
            return False
    
    def set_data_mapping(self, data_dict):
        """تعيين البيانات للتعبئة"""
        self.data_mapping = data_dict
        logger.debug(f"تم تعيين البيانات: {list(data_dict.keys())}")
    
    def play_actions(self, start_index=0, end_index=None):
        """تشغيل الإجراءات"""
        if not self.loaded_actions:
            logger.error("لا توجد إجراءات محملة")
            return False
        
        self.is_playing = True
        self.current_action_index = start_index
        
        if end_index is None:
            end_index = len(self.loaded_actions)
        
        try:
            for i in range(start_index, min(end_index, len(self.loaded_actions))):
                if not self.is_playing:
                    break
                
                action = self.loaded_actions[i]
                self.current_action_index = i
                
                success = self._execute_action(action)
                if not success:
                    logger.warning(f"فشل في تنفيذ الإجراء {i}: {action.get('type', 'unknown')}")
                
                # تأخير بين الإجراءات
                time.sleep(RECORDING_CONFIG["delay_between_actions"])
            
            logger.info("تم تشغيل جميع الإجراءات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل الإجراءات: {str(e)}")
            return False
        finally:
            self.is_playing = False
    
    def _execute_action(self, action):
        """تنفيذ إجراء واحد"""
        action_type = action.get("type")
        
        try:
            if action_type == "click":
                return self._execute_click(action)
            elif action_type == "input":
                return self._execute_input(action)
            elif action_type == "select":
                return self._execute_select(action)
            elif action_type in ["session_start", "session_end"]:
                return True  # تجاهل إجراءات الجلسة
            else:
                logger.warning(f"نوع إجراء غير معروف: {action_type}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الإجراء {action_type}: {str(e)}")
            return False
    
    def _execute_click(self, action):
        """تنفيذ النقر"""
        element = self._find_element(action.get("element", {}))
        if element:
            return self.web_driver.click_element(element)
        return False
    
    def _execute_input(self, action):
        """تنفيذ الإدخال"""
        element = self._find_element(action.get("element", {}))
        if element:
            # الحصول على النص للإدخال
            text = self._get_input_text(action)
            return self.web_driver.send_keys_to_element(element, text)
        return False
    
    def _execute_select(self, action):
        """تنفيذ الاختيار من قائمة منسدلة"""
        element = self._find_element(action.get("element", {}))
        if element:
            try:
                select = Select(element)
                value = action.get("selected_value", "")
                
                # محاولة الاختيار بالقيمة أولاً
                try:
                    select.select_by_value(value)
                except:
                    # إذا فشل، محاولة الاختيار بالنص
                    select.select_by_visible_text(value)
                
                return True
            except Exception as e:
                logger.error(f"خطأ في الاختيار من القائمة: {str(e)}")
                return False
        return False
    
    def _find_element(self, element_info):
        """البحث عن العنصر"""
        if not element_info:
            return None
        
        # محاولة البحث بالـ ID أولاً
        if element_info.get("id"):
            element = self.web_driver.find_element(By.ID, element_info["id"])
            if element:
                return element
        
        # محاولة البحث بالـ name
        if element_info.get("name"):
            element = self.web_driver.find_element(By.NAME, element_info["name"])
            if element:
                return element
        
        # محاولة البحث بالـ XPath
        if element_info.get("xpath"):
            element = self.web_driver.find_element(By.XPATH, element_info["xpath"])
            if element:
                return element
        
        # محاولة البحث بالـ class
        if element_info.get("class"):
            element = self.web_driver.find_element(By.CLASS_NAME, element_info["class"])
            if element:
                return element
        
        logger.warning(f"لم يتم العثور على العنصر: {element_info}")
        return None
    
    def _get_input_text(self, action):
        """الحصول على النص للإدخال من البيانات المعينة"""
        original_text = action.get("text", "")
        element_info = action.get("element", {})
        
        # محاولة تحديد نوع الحقل من معلومات العنصر
        field_name = self._identify_field_name(element_info)
        
        if field_name and field_name in self.data_mapping:
            return str(self.data_mapping[field_name])
        
        # إذا لم نجد مطابقة، استخدم النص الأصلي
        return original_text
    
    def _identify_field_name(self, element_info):
        """تحديد اسم الحقل من معلومات العنصر"""
        # البحث في الخصائص المختلفة للعنصر
        for attr in ["name", "id", "placeholder"]:
            value = element_info.get(attr, "").lower()
            if value:
                # البحث عن مطابقات في أسماء الأعمدة
                for column_name in self.data_mapping.keys():
                    if column_name.lower() in value or value in column_name.lower():
                        return column_name
        
        return None
    
    def stop_playing(self):
        """إيقاف تشغيل الإجراءات"""
        self.is_playing = False
        logger.info("تم إيقاف تشغيل الإجراءات")
    
    def get_progress(self):
        """الحصول على نسبة التقدم"""
        if not self.loaded_actions:
            return 0
        return (self.current_action_index / len(self.loaded_actions)) * 100
    
    def get_current_action(self):
        """الحصول على الإجراء الحالي"""
        if 0 <= self.current_action_index < len(self.loaded_actions):
            return self.loaded_actions[self.current_action_index]
        return None
