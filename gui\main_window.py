# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pandas as pd
from pathlib import Path
import threading
import webbrowser
import time
from datetime import datetime
import tkinter.font as tkFont
try:
    from tkinter import dnd
except ImportError:
    dnd = None
from config import UI_CONFIG
from data_management.data_manager import DataManager
from data_management.session_manager import SessionManager
from automation.web_driver import WebDriverManager
from automation.recorder import ActionRecorder
from automation.player import ActionPlayer
from gui.real_webview import RealWebView
from utils.logger import logger


class MainWindow:
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # إنشاء المكونات الأساسية
        self.data_manager = DataManager()
        self.session_manager = SessionManager()
        self.web_driver = WebDriverManager()
        self.recorder = None
        self.player = None
        
        # متغيرات الحالة
        self.is_recording = False
        self.is_playing = False
        self.current_recording_count = 0
        
        self.create_widgets()
        self.setup_layout()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(UI_CONFIG["window_title"])
        self.root.geometry(UI_CONFIG["window_size"])
        
        # تعيين الخط
        default_font = (UI_CONFIG["font_family"], UI_CONFIG["font_size"])
        self.root.option_add("*Font", default_font)
        
        # منع تغيير حجم النافذة
        self.root.resizable(True, True)
        
        # إعداد إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # إنشاء الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء الإطارات الفرعية
        self.create_data_frame(main_frame)
        self.create_control_frame(main_frame)
        self.create_embedded_browser(main_frame)
        self.create_status_frame(main_frame)
    
    def create_data_frame(self, parent):
        """إنشاء إطار البيانات"""
        # إطار البيانات
        data_frame = ttk.LabelFrame(parent, text="البيانات", padding=10)
        data_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # أزرار تحميل البيانات
        btn_frame = ttk.Frame(data_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.btn_load_data = ttk.Button(
            btn_frame, 
            text="تحميل ملف البيانات", 
            command=self.load_data_file
        )
        self.btn_load_data.pack(side=tk.LEFT, padx=(0, 5))
        
        self.btn_refresh_data = ttk.Button(
            btn_frame, 
            text="تحديث", 
            command=self.refresh_data_display
        )
        self.btn_refresh_data.pack(side=tk.LEFT)
        
        # جدول البيانات
        self.create_data_table(data_frame)
        
        # معلومات البيانات
        info_frame = ttk.Frame(data_frame)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.lbl_data_info = ttk.Label(info_frame, text="لم يتم تحميل بيانات")
        self.lbl_data_info.pack(side=tk.LEFT)
        
        # شريط التقدم
        self.progress_data = ttk.Progressbar(info_frame, mode='determinate')
        self.progress_data.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات مع إمكانية النسخ واللصق"""
        # إطار الجدول مع أشرطة التمرير
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview
        columns = ("الرقم", "البيانات")
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)

        # ربط أحداث النسخ واللصق والتحديد
        self.data_tree.bind('<Control-c>', self.copy_selection)
        self.data_tree.bind('<Control-v>', self.paste_selection)
        self.data_tree.bind('<Control-a>', self.select_all)
        self.data_tree.bind('<Button-3>', self.show_context_menu)  # النقر بالزر الأيمن
        self.data_tree.bind('<Double-1>', self.on_double_click)  # النقر المزدوج
        self.data_tree.bind('<Button-1>', self.on_single_click)  # النقر المفرد

        # إنشاء قائمة السياق المحسنة
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📋 نسخ الخلية", command=self.copy_single_cell)
        self.context_menu.add_command(label="📄 نسخ الصف", command=self.copy_selected_row)
        self.context_menu.add_command(label="📊 نسخ التحديد", command=self.copy_selection)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="✅ تحديد الكل", command=self.select_all)
        self.context_menu.add_command(label="🔍 البحث", command=self.search_in_data)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="💾 تصدير إلى CSV", command=self.export_to_csv)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)

        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.data_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def create_control_frame(self, parent):
        """إنشاء إطار التحكم"""
        control_frame = ttk.LabelFrame(parent, text="التحكم", padding=10)
        control_frame.grid(row=1, column=0, sticky="ew", padx=(0, 5), pady=(10, 0))

        # إعدادات المتصفح مع أزرار النسخ واللصق
        browser_frame = ttk.Frame(control_frame)
        browser_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(browser_frame, text="رابط الموقع:").pack(side=tk.LEFT)

        # إطار الرابط مع الأزرار
        url_input_frame = ttk.Frame(browser_frame)
        url_input_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.entry_url = ttk.Entry(url_input_frame, font=("Arial", 9))
        self.entry_url.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.entry_url.insert(0, "https://quickly24erp.com")

        # أزرار النسخ واللصق للرابط الرئيسي
        url_buttons_frame = ttk.Frame(url_input_frame)
        url_buttons_frame.pack(side=tk.RIGHT, padx=(5, 0))

        self.btn_copy_main_url = ttk.Button(
            url_buttons_frame,
            text="📋",
            width=3,
            command=self.copy_main_url
        )
        self.btn_copy_main_url.pack(side=tk.LEFT, padx=(2, 0))

        self.btn_paste_main_url = ttk.Button(
            url_buttons_frame,
            text="📄",
            width=3,
            command=self.paste_main_url
        )
        self.btn_paste_main_url.pack(side=tk.LEFT, padx=(2, 0))

        self.btn_open_browser = ttk.Button(
            browser_frame,
            text="🌐 فتح المتصفح",
            command=self.open_browser
        )
        self.btn_open_browser.pack(side=tk.RIGHT)

        # أزرار التسجيل
        recording_frame = ttk.Frame(control_frame)
        recording_frame.pack(fill=tk.X, pady=(0, 10))

        self.btn_start_recording = ttk.Button(
            recording_frame,
            text="بدء التسجيل",
            command=self.start_recording
        )
        self.btn_start_recording.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_recording = ttk.Button(
            recording_frame,
            text="إيقاف التسجيل",
            command=self.stop_recording,
            state=tk.DISABLED
        )
        self.btn_stop_recording.pack(side=tk.LEFT, padx=(0, 5))

        self.lbl_recording_status = ttk.Label(recording_frame, text="غير مسجل")
        self.lbl_recording_status.pack(side=tk.LEFT, padx=(10, 0))

        # أزرار التشغيل
        playback_frame = ttk.Frame(control_frame)
        playback_frame.pack(fill=tk.X)

        self.btn_start_automation = ttk.Button(
            playback_frame,
            text="بدء التعبئة التلقائية",
            command=self.start_automation
        )
        self.btn_start_automation.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_automation = ttk.Button(
            playback_frame,
            text="إيقاف التعبئة",
            command=self.stop_automation,
            state=tk.DISABLED
        )
        self.btn_stop_automation.pack(side=tk.LEFT, padx=(0, 5))

        self.lbl_automation_status = ttk.Label(playback_frame, text="متوقف")
        self.lbl_automation_status.pack(side=tk.LEFT, padx=(10, 0))

    def create_browser_frame(self, parent):
        """إنشاء إطار المتصفح مع عرض الموقع داخلياً"""
        browser_frame = ttk.LabelFrame(parent, text="المتصفح والتحكم", padding=10)
        browser_frame.grid(row=0, column=1, rowspan=2, sticky="nsew", padx=(5, 0))

        # معلومات المتصفح
        info_frame = ttk.Frame(browser_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.lbl_browser_status = ttk.Label(info_frame, text="المتصفح مغلق")
        self.lbl_browser_status.pack(side=tk.LEFT)

        self.btn_close_browser = ttk.Button(
            info_frame,
            text="إغلاق المتصفح",
            command=self.close_browser,
            state=tk.DISABLED
        )
        self.btn_close_browser.pack(side=tk.RIGHT)

        # إطار اختيار التسجيلات
        recordings_frame = ttk.LabelFrame(browser_frame, text="التسجيلات المحفوظة", padding=5)
        recordings_frame.pack(fill=tk.X, pady=(0, 10))

        # قائمة التسجيلات
        self.recordings_var = tk.StringVar()
        self.recordings_combo = ttk.Combobox(
            recordings_frame,
            textvariable=self.recordings_var,
            state="readonly",
            width=30
        )
        self.recordings_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.btn_load_recording = ttk.Button(
            recordings_frame,
            text="تحميل التسجيل",
            command=self.load_selected_recording
        )
        self.btn_load_recording.pack(side=tk.RIGHT)

        # منطقة عرض معلومات الصفحة والتحكم
        control_notebook = ttk.Notebook(browser_frame)
        control_notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب معلومات الصفحة
        page_info_frame = ttk.Frame(control_notebook)
        control_notebook.add(page_info_frame, text="معلومات الصفحة")

        self.text_page_info = tk.Text(
            page_info_frame,
            height=12,
            width=50,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.text_page_info.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # شريط تمرير للنص
        page_scrollbar = ttk.Scrollbar(page_info_frame, command=self.text_page_info.yview)
        self.text_page_info.configure(yscrollcommand=page_scrollbar.set)

        # تبويب عرض الموقع المدمج
        browser_view_frame = ttk.Frame(control_notebook)
        control_notebook.add(browser_view_frame, text="عرض الموقع")

        # شريط الرابط مع أزرار النسخ واللصق
        url_frame = ttk.Frame(browser_view_frame)
        url_frame.pack(fill=tk.X, pady=(5, 10))

        ttk.Label(url_frame, text="الرابط:").pack(side=tk.LEFT, padx=(0, 5))

        self.url_display_var = tk.StringVar()
        self.url_display_entry = ttk.Entry(
            url_frame,
            textvariable=self.url_display_var,
            state="readonly",
            font=("Arial", 9)
        )
        self.url_display_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # أزرار النسخ واللصق
        self.btn_copy_url = ttk.Button(
            url_frame,
            text="📋 نسخ",
            width=8,
            command=self.copy_url
        )
        self.btn_copy_url.pack(side=tk.LEFT, padx=(0, 2))

        self.btn_paste_url = ttk.Button(
            url_frame,
            text="📄 لصق",
            width=8,
            command=self.paste_url
        )
        self.btn_paste_url.pack(side=tk.LEFT, padx=(0, 2))

        self.btn_go_to_url = ttk.Button(
            url_frame,
            text="🔗 انتقال",
            width=8,
            command=self.go_to_pasted_url
        )
        self.btn_go_to_url.pack(side=tk.LEFT)

        # إطار عرض الموقع المحسن
        self.browser_display = tk.Text(
            browser_view_frame,
            height=10,
            width=50,
            wrap=tk.WORD,
            bg="#f8f9fa",
            fg="#212529",
            font=("Consolas", 9),
            state=tk.DISABLED,
            relief=tk.SUNKEN,
            bd=2
        )
        self.browser_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # شريط تمرير للعرض
        browser_scrollbar = ttk.Scrollbar(browser_view_frame, command=self.browser_display.yview)
        self.browser_display.configure(yscrollcommand=browser_scrollbar.set)

        # أزرار التحكم في العرض
        view_controls = ttk.Frame(browser_view_frame)
        view_controls.pack(fill=tk.X, pady=(5, 0))

        self.btn_refresh_view = ttk.Button(
            view_controls,
            text="🔄 تحديث العرض",
            command=self.refresh_browser_view
        )
        self.btn_refresh_view.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_take_screenshot = ttk.Button(
            view_controls,
            text="📸 لقطة شاشة",
            command=self.take_screenshot
        )
        self.btn_take_screenshot.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_get_page_source = ttk.Button(
            view_controls,
            text="📄 مصدر الصفحة",
            command=self.get_page_source
        )
        self.btn_get_page_source.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_detect_elements = ttk.Button(
            view_controls,
            text="🔍 كشف العناصر",
            command=self.detect_page_elements
        )
        self.btn_detect_elements.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_open_external = ttk.Button(
            view_controls,
            text="🌐 فتح خارجي",
            command=self.open_in_external_browser
        )
        self.btn_open_external.pack(side=tk.RIGHT)

    def create_embedded_browser(self, parent):
        """إنشاء المتصفح المدمج الجديد"""
        # إطار المتصفح المدمج
        browser_container = ttk.LabelFrame(parent, text="المتصفح المدمج", padding=5)
        browser_container.grid(row=0, column=1, rowspan=2, sticky="nsew", padx=(5, 0))

        # إنشاء المتصفح المدمج الحقيقي الجديد
        self.real_webview = RealWebView(browser_container, self.data_manager)
        self.real_webview.pack(fill=tk.BOTH, expand=True)

        # ربط الأحداث
        self.real_webview.status_var.trace('w', self._on_browser_status_change)

    def create_status_frame(self, parent):
        """إنشاء إطار الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))

        # شريط الحالة
        self.lbl_status = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN)
        self.lbl_status.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # شريط التقدم العام
        self.progress_general = ttk.Progressbar(status_frame, mode='determinate')
        self.progress_general.pack(side=tk.RIGHT, padx=(10, 0))

    def setup_layout(self):
        """إعداد تخطيط النافذة"""
        # تكوين الشبكة
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # تكوين الإطار الرئيسي
        main_frame = self.root.children['!frame']
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=2)

    # وظائف تحميل البيانات
    def load_data_file(self):
        """تحميل ملف البيانات"""
        file_types = [
            ("Excel files", "*.xlsx *.xls"),
            ("CSV files", "*.csv"),
            ("All files", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="اختر ملف البيانات",
            filetypes=file_types
        )

        if file_path:
            if self.data_manager.load_data(file_path):
                self.refresh_data_display()
                self.update_status("تم تحميل البيانات بنجاح")
                messagebox.showinfo("نجح", "تم تحميل البيانات بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل في تحميل البيانات")

    def refresh_data_display(self):
        """تحديث عرض البيانات"""
        # مسح الجدول
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        if not self.data_manager.is_data_loaded():
            self.lbl_data_info.config(text="لم يتم تحميل بيانات")
            return

        # تحديث عناوين الأعمدة
        columns = ["الرقم"] + self.data_manager.get_column_names()
        self.data_tree.config(columns=columns)

        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)

        # إضافة البيانات
        preview_data = self.data_manager.get_data_preview(20)  # عرض أول 20 صف

        for index, row in preview_data.iterrows():
            values = [str(index + 1)] + [str(value) for value in row.values]
            self.data_tree.insert("", tk.END, values=values)

        # تحديث معلومات البيانات
        total_rows = self.data_manager.total_rows
        current_row = self.data_manager.current_row + 1

        info_text = f"الصفوف: {total_rows} | الحالي: {current_row}"
        self.lbl_data_info.config(text=info_text)

        # تحديث شريط التقدم
        progress = self.data_manager.get_progress()
        self.progress_data.config(value=progress)

    # وظائف المتصفح المحدثة
    def _on_browser_status_change(self, *args):
        """معالجة تغيير حالة المتصفح المدمج"""
        try:
            status = self.real_webview.status_var.get()
            self.update_status(status)
        except Exception as e:
            logger.error(f"خطأ في معالجة حالة المتصفح: {str(e)}")

    def open_browser(self):
        """فتح المتصفح - تحديث للمتصفح المدمج"""
        url = self.entry_url.get().strip()
        if not url:
            messagebox.showerror("خطأ", "يرجى إدخال رابط الموقع")
            return

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.entry_url.delete(0, tk.END)
            self.entry_url.insert(0, url)

        # استخدام المتصفح المدمج الحقيقي الجديد
        self.real_webview.url_var.set(url)
        self.real_webview.navigate_to_url()

        # تحديث حالة الأزرار
        self.btn_open_browser.config(state=tk.DISABLED)
        self.btn_start_recording.config(state=tk.NORMAL)

    def _open_browser_thread(self, url):
        """فتح المتصفح في خيط منفصل"""
        try:
            self.update_status("جاري فتح المتصفح...")

            if self.web_driver.initialize_driver():
                if self.web_driver.navigate_to(url):
                    # تحديث الواجهة في الخيط الرئيسي
                    self.root.after(0, self._on_browser_opened)
                    self.update_page_info()
                else:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", "فشل في الانتقال للموقع"))
            else:
                self.root.after(0, lambda: messagebox.showerror("خطأ", "فشل في فتح المتصفح"))

        except Exception as e:
            logger.error(f"خطأ في فتح المتصفح: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في فتح المتصفح: {str(e)}"))

    def _on_browser_opened(self):
        """تحديث الواجهة عند فتح المتصفح"""
        self.lbl_browser_status.config(text="المتصفح مفتوح")
        self.btn_close_browser.config(state=tk.NORMAL)
        self.btn_open_browser.config(state=tk.DISABLED)
        self.btn_start_recording.config(state=tk.NORMAL)
        self.update_status("تم فتح المتصفح بنجاح")

        # إنشاء مسجل الإجراءات
        self.recorder = ActionRecorder(self.web_driver)
        self.player = ActionPlayer(self.web_driver)

        # تحديث عرض المتصفح
        self.refresh_browser_view()

        # تحديث قائمة التسجيلات
        self.refresh_recordings_list()

    def close_browser(self):
        """إغلاق المتصفح المدمج"""
        try:
            # إغلاق المتصفح المدمج الحقيقي الجديد
            self.real_webview.close_browser()

            # تحديث حالة الأزرار
            self.btn_open_browser.config(state=tk.NORMAL)
            self.btn_start_recording.config(state=tk.DISABLED)
            self.btn_start_automation.config(state=tk.DISABLED)

            self.update_status("تم إغلاق المتصفح")

        except Exception as e:
            logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في إغلاق المتصفح: {str(e)}")

    def update_page_info(self):
        """تحديث معلومات الصفحة"""
        if not self.web_driver or not self.web_driver.is_initialized:
            return

        try:
            current_url = self.web_driver.get_current_url()
            page_title = self.web_driver.get_page_title()

            info_text = f"العنوان: {page_title}\n"
            info_text += f"الرابط: {current_url}\n"
            info_text += f"الوقت: {datetime.now().strftime('%H:%M:%S')}\n"
            info_text += "-" * 50 + "\n"

            self.text_page_info.config(state=tk.NORMAL)
            self.text_page_info.insert(tk.END, info_text)
            self.text_page_info.see(tk.END)
            self.text_page_info.config(state=tk.DISABLED)

        except Exception as e:
            logger.error(f"خطأ في تحديث معلومات الصفحة: {str(e)}")

    # وظائف التسجيل
    def start_recording(self):
        """بدء تسجيل الإجراءات"""
        if not self.recorder:
            messagebox.showerror("خطأ", "يجب فتح المتصفح أولاً")
            return

        if self.current_recording_count >= 3:
            messagebox.showwarning("تحذير", "تم الوصول للحد الأقصى من التسجيلات (3)")
            return

        session_name = f"recording_{self.current_recording_count + 1}"
        self.recorder.start_recording(session_name)

        self.is_recording = True
        self.current_recording_count += 1

        # تحديث الواجهة
        self.btn_start_recording.config(state=tk.DISABLED)
        self.btn_stop_recording.config(state=tk.NORMAL)
        self.lbl_recording_status.config(text=f"جاري التسجيل ({self.current_recording_count}/3)")

        self.update_status(f"بدء التسجيل رقم {self.current_recording_count}")

        # بدء مراقبة الإجراءات
        self.monitor_user_actions()

    def stop_recording(self):
        """إيقاف تسجيل الإجراءات"""
        if not self.recorder or not self.is_recording:
            return

        self.recorder.stop_recording()
        self.is_recording = False

        # حفظ التسجيل
        saved_file = self.recorder.save_recording()
        if saved_file:
            self.session_manager.load_sessions_list()  # تحديث قائمة الجلسات
            self.refresh_recordings_list()  # تحديث قائمة التسجيلات في الواجهة

        # تحديث الواجهة
        self.btn_start_recording.config(state=tk.NORMAL if self.current_recording_count < 3 else tk.DISABLED)
        self.btn_stop_recording.config(state=tk.DISABLED)
        self.lbl_recording_status.config(text=f"تم التسجيل ({self.current_recording_count}/3)")

        # تفعيل زر التشغيل إذا تم التسجيل 3 مرات
        if self.current_recording_count >= 3:
            self.btn_start_automation.config(state=tk.NORMAL)
            messagebox.showinfo("مكتمل", "تم إكمال التسجيلات الثلاث. يمكنك الآن بدء التعبئة التلقائية.")

        self.update_status(f"تم حفظ التسجيل رقم {self.current_recording_count}")

    def monitor_user_actions(self):
        """مراقبة إجراءات المستخدم (محاكاة)"""
        # هذه الوظيفة ستحتاج لتطوير أكثر تعقيداً لمراقبة الإجراءات الفعلية
        # حالياً هي مجرد عنصر نائب
        if self.is_recording:
            # جدولة المراقبة التالية
            self.root.after(1000, self.monitor_user_actions)

    # وظائف التشغيل التلقائي
    def start_automation(self):
        """بدء التعبئة التلقائية باستخدام المتصفح المدمج"""
        if not self.data_manager.is_data_loaded():
            messagebox.showerror("خطأ", "يجب تحميل ملف البيانات أولاً")
            return

        if not self.real_webview.web_driver:
            messagebox.showerror("خطأ", "يجب فتح المتصفح أولاً")
            return

        # استخدام التعبئة التلقائية المدمجة
        self.real_webview.start_auto_fill()

        # تحديث حالة الأزرار
        self.btn_start_automation.config(state=tk.DISABLED)
        self.btn_stop_automation.config(state=tk.NORMAL)
        self.lbl_automation_status.config(text="جاري التعبئة...")

    def _automation_thread(self):
        """خيط التعبئة التلقائية"""
        try:
            self.data_manager.reset_position()

            while self.is_playing and self.data_manager.get_remaining_rows() > 0:
                # الحصول على بيانات الصف الحالي
                row_data = self.data_manager.get_current_row_data()
                if not row_data:
                    break

                # تعيين البيانات للمشغل
                self.player.set_data_mapping(row_data)

                # تشغيل الإجراءات
                success = self.player.play_actions()

                if success:
                    # الانتقال للصف التالي
                    self.data_manager.get_next_row_data()

                    # تحديث التقدم
                    progress = self.data_manager.get_progress()
                    self.root.after(0, lambda p=progress: self.progress_general.config(value=p))
                    self.root.after(0, self.refresh_data_display)

                    # تأخير بين الصفوف
                    time.sleep(2)
                else:
                    logger.warning("فشل في تعبئة الصف")
                    break

            # انتهاء التعبئة
            self.root.after(0, self._on_automation_finished)

        except Exception as e:
            logger.error(f"خطأ في التعبئة التلقائية: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في التعبئة: {str(e)}"))
            self.root.after(0, self._on_automation_finished)

    def _on_automation_finished(self):
        """عند انتهاء التعبئة التلقائية"""
        self.is_playing = False
        self.btn_start_automation.config(state=tk.NORMAL)
        self.btn_stop_automation.config(state=tk.DISABLED)
        self.lbl_automation_status.config(text="مكتمل")
        self.progress_general.config(value=100)
        self.update_status("تم إكمال التعبئة التلقائية")
        messagebox.showinfo("مكتمل", "تم إكمال التعبئة التلقائية بنجاح")

    def stop_automation(self):
        """إيقاف التعبئة التلقائية"""
        # إيقاف التعبئة في المتصفح المدمج الحقيقي الجديد
        self.real_webview.stop_automation()

        # تحديث حالة الأزرار
        self.btn_start_automation.config(state=tk.NORMAL)
        self.btn_stop_automation.config(state=tk.DISABLED)
        self.lbl_automation_status.config(text="متوقف")
        self.update_status("تم إيقاف التعبئة التلقائية")

    # وظائف مساعدة
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.lbl_status.config(text=message)
        logger.info(message)

    def on_closing(self):
        """عند إغلاق النافذة"""
        try:
            # إيقاف جميع العمليات
            self.is_recording = False

            # إغلاق المتصفح المدمج الحقيقي الجديد
            if hasattr(self, 'real_webview'):
                self.real_webview.close_browser()

            # إغلاق النافذة
            self.root.destroy()
            logger.info("تم إغلاق البرنامج بنجاح")

        except Exception as e:
            logger.error(f"خطأ عند الإغلاق: {str(e)}")
            self.root.destroy()

    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

    # وظائف النسخ واللصق
    def copy_selection(self, event=None):
        """نسخ البيانات المحددة"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                return

            copied_data = []
            for item in selected_items:
                values = self.data_tree.item(item)['values']
                copied_data.append('\t'.join(str(v) for v in values))

            clipboard_text = '\n'.join(copied_data)
            self.root.clipboard_clear()
            self.root.clipboard_append(clipboard_text)

            self.update_status(f"تم نسخ {len(selected_items)} صف")

        except Exception as e:
            logger.error(f"خطأ في النسخ: {str(e)}")

    def paste_selection(self, event=None):
        """لصق البيانات"""
        try:
            clipboard_text = self.root.clipboard_get()
            self.update_status("تم لصق البيانات من الحافظة")

        except tk.TclError:
            self.update_status("لا توجد بيانات في الحافظة")
        except Exception as e:
            logger.error(f"خطأ في اللصق: {str(e)}")

    def select_all(self):
        """تحديد جميع البيانات"""
        try:
            all_items = self.data_tree.get_children()
            self.data_tree.selection_set(all_items)
            self.update_status(f"تم تحديد {len(all_items)} صف")

        except Exception as e:
            logger.error(f"خطأ في تحديد الكل: {str(e)}")

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            # تحديد العنصر المنقور عليه
            item = self.data_tree.identify_row(event.y)
            if item:
                self.data_tree.selection_set(item)
                self.data_tree.focus(item)

            self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة السياق: {str(e)}")

    def on_single_click(self, event):
        """التعامل مع النقر المفرد"""
        try:
            # تحديد العمود والصف المنقور عليه
            item = self.data_tree.identify_row(event.y)
            column = self.data_tree.identify_column(event.x)

            if item and column:
                self.selected_item = item
                self.selected_column = column

        except Exception as e:
            logger.error(f"خطأ في النقر المفرد: {str(e)}")

    def on_double_click(self, event):
        """التعامل مع النقر المزدوج - نسخ الخلية"""
        try:
            item = self.data_tree.identify_row(event.y)
            column = self.data_tree.identify_column(event.x)

            if item and column:
                # الحصول على قيمة الخلية
                column_index = int(column.replace('#', '')) - 1
                values = self.data_tree.item(item)['values']

                if 0 <= column_index < len(values):
                    cell_value = str(values[column_index])

                    # نسخ القيمة
                    self.root.clipboard_clear()
                    self.root.clipboard_append(cell_value)

                    self.update_status(f"تم نسخ الخلية: {cell_value[:50]}...")
                    self.show_temporary_message("📋 تم نسخ الخلية!")

        except Exception as e:
            logger.error(f"خطأ في النقر المزدوج: {str(e)}")

    def copy_single_cell(self):
        """نسخ خلية واحدة"""
        try:
            if hasattr(self, 'selected_item') and hasattr(self, 'selected_column'):
                item = self.selected_item
                column = self.selected_column

                column_index = int(column.replace('#', '')) - 1
                values = self.data_tree.item(item)['values']

                if 0 <= column_index < len(values):
                    cell_value = str(values[column_index])

                    self.root.clipboard_clear()
                    self.root.clipboard_append(cell_value)

                    self.update_status(f"تم نسخ الخلية: {cell_value[:50]}...")
                    self.show_temporary_message("📋 تم نسخ الخلية!")
            else:
                messagebox.showwarning("تحذير", "يرجى تحديد خلية أولاً")

        except Exception as e:
            logger.error(f"خطأ في نسخ الخلية: {str(e)}")

    def copy_selected_row(self):
        """نسخ الصف المحدد"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("تحذير", "يرجى تحديد صف أولاً")
                return

            # نسخ أول صف محدد
            item = selected_items[0]
            values = self.data_tree.item(item)['values']
            row_text = '\t'.join(str(v) for v in values)

            self.root.clipboard_clear()
            self.root.clipboard_append(row_text)

            self.update_status(f"تم نسخ الصف")
            self.show_temporary_message("📄 تم نسخ الصف!")

        except Exception as e:
            logger.error(f"خطأ في نسخ الصف: {str(e)}")

    def search_in_data(self):
        """البحث في البيانات"""
        try:
            search_text = tk.simpledialog.askstring("البحث", "أدخل النص للبحث عنه:")
            if search_text:
                self.highlight_search_results(search_text)

        except Exception as e:
            logger.error(f"خطأ في البحث: {str(e)}")

    def highlight_search_results(self, search_text):
        """تمييز نتائج البحث"""
        try:
            # مسح التحديد السابق
            self.data_tree.selection_remove(self.data_tree.selection())

            found_items = []
            search_text = search_text.lower()

            # البحث في جميع العناصر
            for item in self.data_tree.get_children():
                values = self.data_tree.item(item)['values']
                for value in values:
                    if search_text in str(value).lower():
                        found_items.append(item)
                        break

            if found_items:
                # تحديد النتائج
                self.data_tree.selection_set(found_items)
                # التمرير للنتيجة الأولى
                self.data_tree.see(found_items[0])

                self.update_status(f"تم العثور على {len(found_items)} نتيجة")
                messagebox.showinfo("نتائج البحث", f"تم العثور على {len(found_items)} نتيجة")
            else:
                messagebox.showinfo("نتائج البحث", "لم يتم العثور على نتائج")

        except Exception as e:
            logger.error(f"خطأ في تمييز النتائج: {str(e)}")

    def export_to_csv(self):
        """تصدير البيانات إلى CSV"""
        try:
            if not self.data_manager.is_data_loaded():
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ البيانات"
            )

            if file_path:
                # تصدير البيانات
                data = self.data_manager.data
                data.to_csv(file_path, index=False, encoding='utf-8-sig')

                self.update_status(f"تم تصدير البيانات إلى: {file_path}")
                messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            logger.error(f"خطأ في التصدير: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في التصدير: {str(e)}")

    # وظائف التسجيلات والعرض الجديدة
    def load_selected_recording(self):
        """تحميل التسجيل المحدد"""
        try:
            selected_recording = self.recordings_var.get()
            if not selected_recording:
                messagebox.showwarning("تحذير", "يرجى اختيار تسجيل أولاً")
                return

            # البحث عن ملف التسجيل
            sessions = self.session_manager.get_sessions_list()
            selected_session = None

            for session in sessions:
                if session["session_name"] == selected_recording:
                    selected_session = session
                    break

            if selected_session:
                if self.player and self.player.load_recording(selected_session["file_path"]):
                    self.update_status(f"تم تحميل التسجيل: {selected_recording}")
                    messagebox.showinfo("نجح", f"تم تحميل التسجيل: {selected_recording}")
                else:
                    messagebox.showerror("خطأ", "فشل في تحميل التسجيل")
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على التسجيل")

        except Exception as e:
            logger.error(f"خطأ في تحميل التسجيل: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في تحميل التسجيل: {str(e)}")

    def refresh_recordings_list(self):
        """تحديث قائمة التسجيلات"""
        try:
            self.session_manager.load_sessions_list()
            sessions = self.session_manager.get_sessions_list()

            recording_names = [session["session_name"] for session in sessions]
            self.recordings_combo['values'] = recording_names

            if recording_names:
                self.recordings_combo.set(recording_names[0])  # اختيار أول تسجيل

        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة التسجيلات: {str(e)}")

    def refresh_browser_view(self):
        """تحديث عرض المتصفح مع محتوى الصفحة الفعلي"""
        try:
            if not self.web_driver or not self.web_driver.is_initialized:
                self.browser_display.config(state=tk.NORMAL)
                self.browser_display.delete(1.0, tk.END)
                self.browser_display.insert(tk.END, "🚫 المتصفح غير مفتوح\n\n")
                self.browser_display.insert(tk.END, "يرجى فتح المتصفح أولاً لعرض محتوى الصفحة\n\n")
                self.browser_display.insert(tk.END, "خطوات البدء:\n")
                self.browser_display.insert(tk.END, "1. أدخل رابط الموقع\n")
                self.browser_display.insert(tk.END, "2. اضغط 'فتح المتصفح'\n")
                self.browser_display.insert(tk.END, "3. اضغط 'تحديث العرض'")
                self.browser_display.config(state=tk.DISABLED)

                # مسح رابط العرض
                self.url_display_var.set("")
                return

            # الحصول على معلومات الصفحة
            current_url = self.web_driver.get_current_url()
            page_title = self.web_driver.get_page_title()

            # تحديث رابط العرض
            self.url_display_var.set(current_url)

            # الحصول على محتوى الصفحة الفعلي
            try:
                page_source = self.web_driver.driver.page_source
                page_text = self._extract_page_text(page_source)
            except Exception as e:
                page_text = f"خطأ في استخراج محتوى الصفحة: {str(e)}"

            # تنسيق المحتوى للعرض
            display_content = f"🌐 {page_title}\n"
            display_content += f"🔗 {current_url}\n"
            display_content += "=" * 80 + "\n\n"
            display_content += "📄 محتوى الصفحة:\n"
            display_content += "-" * 40 + "\n"
            display_content += page_text
            display_content += "\n" + "-" * 40 + "\n"
            display_content += f"🕒 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            display_content += f"📊 حجم المحتوى: {len(page_source)} حرف"

            self.browser_display.config(state=tk.NORMAL)
            self.browser_display.delete(1.0, tk.END)
            self.browser_display.insert(tk.END, display_content)
            self.browser_display.config(state=tk.DISABLED)

            self.update_status("تم تحديث عرض المتصفح مع المحتوى الفعلي")

        except Exception as e:
            logger.error(f"خطأ في تحديث عرض المتصفح: {str(e)}")
            self.browser_display.config(state=tk.NORMAL)
            self.browser_display.delete(1.0, tk.END)
            self.browser_display.insert(tk.END, f"❌ خطأ في تحديث العرض:\n{str(e)}")
            self.browser_display.config(state=tk.DISABLED)

    def _extract_page_text(self, html_source):
        """استخراج النص من مصدر HTML"""
        try:
            import re

            # إزالة العناصر غير المرغوبة
            html_source = re.sub(r'<script.*?</script>', '', html_source, flags=re.DOTALL | re.IGNORECASE)
            html_source = re.sub(r'<style.*?</style>', '', html_source, flags=re.DOTALL | re.IGNORECASE)
            html_source = re.sub(r'<!--.*?-->', '', html_source, flags=re.DOTALL)

            # استخراج النصوص من العناصر المهمة
            important_tags = ['title', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'span', 'a', 'button', 'input', 'label']

            extracted_text = []

            for tag in important_tags:
                pattern = f'<{tag}[^>]*>(.*?)</{tag}>'
                matches = re.findall(pattern, html_source, flags=re.DOTALL | re.IGNORECASE)
                for match in matches:
                    # تنظيف النص
                    clean_text = re.sub(r'<[^>]+>', '', match).strip()
                    if clean_text and len(clean_text) > 2:
                        extracted_text.append(f"• {clean_text}")

            # إزالة التكرارات والنصوص الطويلة جداً
            unique_texts = []
            for text in extracted_text:
                if len(text) < 200 and text not in unique_texts:
                    unique_texts.append(text)

            # أخذ أول 50 عنصر فقط
            if len(unique_texts) > 50:
                unique_texts = unique_texts[:50]
                unique_texts.append("... (تم اقتطاع المحتوى لتوفير مساحة)")

            return '\n'.join(unique_texts) if unique_texts else "لا يوجد محتوى نصي واضح في الصفحة"

        except Exception as e:
            logger.error(f"خطأ في استخراج النص: {str(e)}")
            return f"خطأ في استخراج المحتوى: {str(e)}"

    def take_screenshot(self):
        """أخذ لقطة شاشة من المتصفح"""
        try:
            if not self.web_driver or not self.web_driver.is_initialized:
                messagebox.showwarning("تحذير", "المتصفح غير مفتوح")
                return

            screenshot_path = self.web_driver.take_screenshot()
            if screenshot_path:
                self.update_status(f"تم حفظ لقطة الشاشة: {screenshot_path}")
                messagebox.showinfo("نجح", f"تم حفظ لقطة الشاشة:\n{screenshot_path}")
            else:
                messagebox.showerror("خطأ", "فشل في أخذ لقطة الشاشة")

        except Exception as e:
            logger.error(f"خطأ في أخذ لقطة الشاشة: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في أخذ لقطة الشاشة: {str(e)}")

    # وظائف النسخ واللصق للرابط
    def copy_url(self):
        """نسخ رابط الموقع"""
        try:
            current_url = self.url_display_var.get()
            if current_url:
                self.root.clipboard_clear()
                self.root.clipboard_append(current_url)
                self.update_status("تم نسخ الرابط إلى الحافظة")
                messagebox.showinfo("نجح", "تم نسخ الرابط إلى الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا يوجد رابط لنسخه")

        except Exception as e:
            logger.error(f"خطأ في نسخ الرابط: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في نسخ الرابط: {str(e)}")

    def paste_url(self):
        """لصق رابط من الحافظة"""
        try:
            clipboard_text = self.root.clipboard_get().strip()
            if clipboard_text:
                # التحقق من صحة الرابط
                if not clipboard_text.startswith(('http://', 'https://')):
                    clipboard_text = 'https://' + clipboard_text

                self.entry_url.config(state=tk.NORMAL)
                self.entry_url.delete(0, tk.END)
                self.entry_url.insert(0, clipboard_text)
                self.entry_url.config(state="readonly")

                self.update_status("تم لصق الرابط من الحافظة")
                messagebox.showinfo("نجح", "تم لصق الرابط من الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا توجد بيانات في الحافظة")

        except tk.TclError:
            messagebox.showwarning("تحذير", "لا توجد بيانات في الحافظة")
        except Exception as e:
            logger.error(f"خطأ في لصق الرابط: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في لصق الرابط: {str(e)}")

    def go_to_pasted_url(self):
        """الانتقال للرابط الملصق"""
        try:
            url = self.entry_url.get().strip()
            if url and self.web_driver and self.web_driver.is_initialized:
                if self.web_driver.navigate_to(url):
                    self.refresh_browser_view()
                    self.update_status(f"تم الانتقال إلى: {url}")
                else:
                    messagebox.showerror("خطأ", "فشل في الانتقال للرابط")
            else:
                messagebox.showwarning("تحذير", "يرجى فتح المتصفح أولاً أو إدخال رابط صحيح")

        except Exception as e:
            logger.error(f"خطأ في الانتقال للرابط: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الانتقال للرابط: {str(e)}")

    def get_page_source(self):
        """الحصول على مصدر الصفحة وعرضه"""
        try:
            if not self.web_driver or not self.web_driver.is_initialized:
                messagebox.showwarning("تحذير", "المتصفح غير مفتوح")
                return

            # الحصول على مصدر الصفحة
            page_source = self.web_driver.driver.page_source

            # تنظيف وتنسيق المصدر
            formatted_source = self._format_html_source(page_source)

            # عرض المصدر في النافذة
            self.browser_display.config(state=tk.NORMAL)
            self.browser_display.delete(1.0, tk.END)
            self.browser_display.insert(tk.END, formatted_source)
            self.browser_display.config(state=tk.DISABLED)

            self.update_status("تم عرض مصدر الصفحة")

        except Exception as e:
            logger.error(f"خطأ في الحصول على مصدر الصفحة: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في الحصول على مصدر الصفحة: {str(e)}")

    def _format_html_source(self, html_source):
        """تنسيق مصدر HTML للعرض"""
        try:
            # استخراج النصوص المهمة من HTML
            import re

            # إزالة التعليقات والسكريبت والستايل
            html_source = re.sub(r'<!--.*?-->', '', html_source, flags=re.DOTALL)
            html_source = re.sub(r'<script.*?</script>', '', html_source, flags=re.DOTALL | re.IGNORECASE)
            html_source = re.sub(r'<style.*?</style>', '', html_source, flags=re.DOTALL | re.IGNORECASE)

            # استخراج النصوص
            text_content = re.sub(r'<[^>]+>', '\n', html_source)
            text_content = re.sub(r'\n\s*\n', '\n', text_content)

            # تنظيف النص
            lines = [line.strip() for line in text_content.split('\n') if line.strip()]

            # أخذ أول 100 سطر فقط
            if len(lines) > 100:
                lines = lines[:100]
                lines.append("... (تم اقتطاع المحتوى)")

            return '\n'.join(lines)

        except Exception as e:
            logger.error(f"خطأ في تنسيق HTML: {str(e)}")
            return html_source[:5000] + "..." if len(html_source) > 5000 else html_source

    def open_in_external_browser(self):
        """فتح الصفحة في متصفح خارجي"""
        try:
            current_url = self.url_display_var.get()
            if current_url:
                import webbrowser
                webbrowser.open(current_url)
                self.update_status("تم فتح الصفحة في متصفح خارجي")
            else:
                messagebox.showwarning("تحذير", "لا يوجد رابط لفتحه")

        except Exception as e:
            logger.error(f"خطأ في فتح المتصفح الخارجي: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في فتح المتصفح الخارجي: {str(e)}")

    def detect_page_elements(self):
        """كشف العناصر التفاعلية في الصفحة"""
        try:
            if not self.web_driver or not self.web_driver.is_initialized:
                messagebox.showwarning("تحذير", "المتصفح غير مفتوح")
                return

            # البحث عن العناصر التفاعلية
            elements_info = self._find_interactive_elements()

            # عرض النتائج
            self._display_elements_info(elements_info)

            self.update_status(f"تم كشف {len(elements_info)} عنصر تفاعلي")

        except Exception as e:
            logger.error(f"خطأ في كشف العناصر: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في كشف العناصر: {str(e)}")

    def _find_interactive_elements(self):
        """البحث عن العناصر التفاعلية"""
        try:
            from selenium.webdriver.common.by import By

            elements_info = []

            # أنواع العناصر المختلفة للبحث عنها
            element_types = [
                ("input", "حقول الإدخال"),
                ("button", "الأزرار"),
                ("select", "القوائم المنسدلة"),
                ("textarea", "مناطق النص"),
                ("a", "الروابط"),
                ("form", "النماذج")
            ]

            for tag, description in element_types:
                try:
                    elements = self.web_driver.driver.find_elements(By.TAG_NAME, tag)

                    for i, element in enumerate(elements[:10]):  # أول 10 عناصر فقط
                        try:
                            element_info = {
                                "type": description,
                                "tag": tag,
                                "id": element.get_attribute("id") or f"element_{i}",
                                "name": element.get_attribute("name") or "",
                                "class": element.get_attribute("class") or "",
                                "text": element.text[:50] if element.text else "",
                                "placeholder": element.get_attribute("placeholder") or "",
                                "value": element.get_attribute("value") or "",
                                "visible": element.is_displayed(),
                                "enabled": element.is_enabled(),
                                "location": element.location,
                                "size": element.size
                            }

                            elements_info.append(element_info)

                        except Exception as e:
                            logger.debug(f"خطأ في معالجة عنصر {tag}: {str(e)}")
                            continue

                except Exception as e:
                    logger.debug(f"خطأ في البحث عن {tag}: {str(e)}")
                    continue

            return elements_info

        except Exception as e:
            logger.error(f"خطأ في البحث عن العناصر: {str(e)}")
            return []

    def _display_elements_info(self, elements_info):
        """عرض معلومات العناصر"""
        try:
            if not elements_info:
                display_content = "❌ لم يتم العثور على عناصر تفاعلية في الصفحة\n\n"
                display_content += "تأكد من:\n"
                display_content += "• تحميل الصفحة بالكامل\n"
                display_content += "• وجود نماذج أو حقول إدخال في الصفحة\n"
                display_content += "• عدم وجود حماية JavaScript"
            else:
                display_content = f"🔍 تم العثور على {len(elements_info)} عنصر تفاعلي:\n"
                display_content += "=" * 60 + "\n\n"

                for i, element in enumerate(elements_info, 1):
                    display_content += f"📌 العنصر {i}: {element['type']}\n"
                    display_content += f"   🏷️  المعرف: {element['id']}\n"

                    if element['name']:
                        display_content += f"   📝 الاسم: {element['name']}\n"

                    if element['text']:
                        display_content += f"   📄 النص: {element['text']}\n"

                    if element['placeholder']:
                        display_content += f"   💭 النص التوضيحي: {element['placeholder']}\n"

                    if element['value']:
                        display_content += f"   💾 القيمة: {element['value']}\n"

                    display_content += f"   👁️  مرئي: {'نعم' if element['visible'] else 'لا'}\n"
                    display_content += f"   ✅ مفعل: {'نعم' if element['enabled'] else 'لا'}\n"
                    display_content += f"   📍 الموقع: x={element['location']['x']}, y={element['location']['y']}\n"
                    display_content += f"   📏 الحجم: {element['size']['width']}x{element['size']['height']}\n"
                    display_content += "-" * 40 + "\n"

                display_content += f"\n💡 نصائح:\n"
                display_content += "• استخدم هذه المعلومات لتحسين التسجيل\n"
                display_content += "• العناصر المرئية والمفعلة قابلة للتفاعل\n"
                display_content += "• احفظ المعرفات والأسماء للاستخدام في التعبئة التلقائية"

            # عرض المعلومات في نافذة العرض
            self.browser_display.config(state=tk.NORMAL)
            self.browser_display.delete(1.0, tk.END)
            self.browser_display.insert(tk.END, display_content)
            self.browser_display.config(state=tk.DISABLED)

        except Exception as e:
            logger.error(f"خطأ في عرض معلومات العناصر: {str(e)}")

    def _create_elements_window(self, elements_info):
        """إنشاء نافذة منفصلة لعرض العناصر"""
        try:
            # إنشاء نافذة جديدة
            elements_window = tk.Toplevel(self.root)
            elements_window.title("العناصر التفاعلية في الصفحة")
            elements_window.geometry("800x600")

            # إنشاء جدول للعناصر
            columns = ("النوع", "المعرف", "الاسم", "النص", "مرئي", "مفعل")
            tree = ttk.Treeview(elements_window, columns=columns, show="headings")

            # تعيين عناوين الأعمدة
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            # إضافة البيانات
            for element in elements_info:
                tree.insert("", tk.END, values=(
                    element['type'],
                    element['id'],
                    element['name'][:20],
                    element['text'][:20],
                    "نعم" if element['visible'] else "لا",
                    "نعم" if element['enabled'] else "لا"
                ))

            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # زر الإغلاق
            close_btn = ttk.Button(elements_window, text="إغلاق", command=elements_window.destroy)
            close_btn.pack(pady=10)

        except Exception as e:
            logger.error(f"خطأ في إنشاء نافذة العناصر: {str(e)}")

    # وظائف النسخ واللصق للرابط الرئيسي
    def copy_main_url(self):
        """نسخ الرابط الرئيسي"""
        try:
            url = self.entry_url.get().strip()
            if url:
                self.root.clipboard_clear()
                self.root.clipboard_append(url)
                self.update_status("تم نسخ الرابط الرئيسي")
                # إظهار رسالة مؤقتة
                self.show_temporary_message("📋 تم النسخ!")
            else:
                messagebox.showwarning("تحذير", "لا يوجد رابط لنسخه")

        except Exception as e:
            logger.error(f"خطأ في نسخ الرابط الرئيسي: {str(e)}")

    def paste_main_url(self):
        """لصق رابط في الحقل الرئيسي"""
        try:
            clipboard_text = self.root.clipboard_get().strip()
            if clipboard_text:
                # التحقق من صحة الرابط
                if not clipboard_text.startswith(('http://', 'https://')):
                    clipboard_text = 'https://' + clipboard_text

                self.entry_url.delete(0, tk.END)
                self.entry_url.insert(0, clipboard_text)

                self.update_status("تم لصق الرابط في الحقل الرئيسي")
                self.show_temporary_message("📄 تم اللصق!")
            else:
                messagebox.showwarning("تحذير", "لا توجد بيانات في الحافظة")

        except tk.TclError:
            messagebox.showwarning("تحذير", "لا توجد بيانات في الحافظة")
        except Exception as e:
            logger.error(f"خطأ في لصق الرابط الرئيسي: {str(e)}")

    def show_temporary_message(self, message, duration=2000):
        """عرض رسالة مؤقتة"""
        try:
            # إنشاء نافذة مؤقتة
            temp_window = tk.Toplevel(self.root)
            temp_window.title("")
            temp_window.geometry("150x50")
            temp_window.resizable(False, False)
            temp_window.attributes("-topmost", True)

            # وضع النافذة في المنتصف
            temp_window.geometry("+{}+{}".format(
                self.root.winfo_rootx() + 200,
                self.root.winfo_rooty() + 100
            ))

            # إزالة شريط العنوان
            temp_window.overrideredirect(True)

            # إضافة الرسالة
            label = tk.Label(
                temp_window,
                text=message,
                font=("Arial", 12, "bold"),
                bg="#4CAF50",
                fg="white",
                pady=10
            )
            label.pack(fill=tk.BOTH, expand=True)

            # إغلاق النافذة تلقائياً
            temp_window.after(duration, temp_window.destroy)

        except Exception as e:
            logger.error(f"خطأ في عرض الرسالة المؤقتة: {str(e)}")
