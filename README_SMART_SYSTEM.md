# 🎬 نظام التسجيل الذكي

## نظام ذكي لتسجيل وتشغيل إجراءات تعبئة النماذج تلقائياً

---

## 🌟 المميزات الرئيسية

### 🎯 **تسجيل مثل الفيديو**
- تسجيل كل حركة للماوس والكيبورد في الوقت الفعلي
- التعرف على العناصر عند التحويم عليها
- تسجيل واحد بدلاً من 3 تسجيلات منفصلة
- حفظ الإجراءات بتفاصيل دقيقة

### 🧠 **ذكاء اصطناعي متقدم**
- التعرف التلقائي على عناصر النماذج
- تحليل أنماط الإدخال ذكياً
- مطابقة البيانات مع الحقول تلقائياً
- تكيف مع تغييرات الواجهة

### ⚡ **تشغيل تلقائي سريع**
- تنفيذ الإجراءات على آلاف الصفوف
- معالجة أخطاء ذكية وإعادة محاولة
- شريط تقدم مفصل ومعلومات فورية
- إيقاف وإستئناف في أي وقت

### 📊 **دعم شامل للبيانات**
- ملفات Excel (.xlsx, .xls)
- ملفات CSV بترميزات مختلفة
- معاينة البيانات قبل التشغيل
- تنظيف وتحسين البيانات تلقائياً

---

## 🚀 التثبيت والتشغيل السريع

### الطريقة السهلة (مستحسنة)
```bash
# تحميل النظام
git clone <repository-url>
cd smart-form-filler

# تشغيل النظام مع التثبيت التلقائي
python run_smart_system.py
```

### التثبيت اليدوي
```bash
# تثبيت المتطلبات
pip install -r smart_requirements.txt

# تشغيل النظام
python smart_recorder_system.py
```

---

## 📋 دليل الاستخدام المفصل

### الخطوة 1: فتح الموقع
1. شغل النظام باستخدام `python run_smart_system.py`
2. أدخل رابط الموقع في حقل "الرابط"
3. اضغط "🚀 فتح" لفتح المتصفح
4. انتظر حتى يتم تحميل الصفحة

### الخطوة 2: التسجيل الذكي
1. اضغط "🔴 بدء التسجيل"
2. قم بتعبئة النموذج كما تفعل عادة:
   - انقر على الحقول
   - اكتب البيانات
   - اضغط الأزرار
3. راقب قائمة "الإجراءات المسجلة" على اليمين
4. اضغط "⏹️ إيقاف التسجيل" عند الانتهاء
5. اضغط "💾 حفظ" لحفظ التسجيل

### الخطوة 3: تحميل البيانات
1. اضغط "📁 تحميل البيانات"
2. اختر ملف Excel أو CSV
3. تأكد من ظهور معلومات البيانات

### الخطوة 4: التشغيل التلقائي
1. اضغط "▶️ تشغيل تلقائي"
2. راقب شريط التقدم
3. يمكنك إيقاف التشغيل في أي وقت بـ "⏸️ إيقاف التشغيل"

---

## 🎯 المميزات المتقدمة

### 👁️ **التعرف على العناصر في الوقت الفعلي**
- عند تحريك الماوس، يظهر معلومات العنصر الحالي
- تحديد نوع العنصر (حقل نص، زر، قائمة منسدلة)
- عرض خصائص العنصر (ID, Name, Class)

### 🎬 **تسجيل شامل للإجراءات**
- النقرات مع الموقع الدقيق
- الكتابة مع العنصر المستهدف
- التمرير والحركات الخاصة
- الطوابع الزمنية الدقيقة

### 🔄 **تشغيل ذكي ومرن**
- استبدال النصوص المسجلة بالبيانات الجديدة
- التعامل مع المفاتيح الخاصة (Enter, Tab, إلخ)
- إعادة المحاولة عند الأخطاء
- تقارير مفصلة عن النتائج

---

## ⚙️ الإعدادات والتخصيص

### ملف الإعدادات (`smart_config.py`)
```python
# تخصيص سرعة التشغيل
PLAYBACK_CONFIG["action_delay"] = 0.3  # أسرع
PLAYBACK_CONFIG["row_delay"] = 1.0     # أسرع بين الصفوف

# تخصيص التسجيل
RECORDING_CONFIG["record_mouse_moves"] = True  # تسجيل حركة الماوس

# تخصيص الواجهة
UI_CONFIG["colors"]["primary"] = "#e74c3c"  # لون أحمر
```

### إعدادات متقدمة
- **وضع الأمان**: منع الإجراءات الخطيرة
- **التشفير**: حماية التسجيلات الحساسة
- **الأداء**: تحسين استهلاك الذاكرة
- **السجلات**: تفصيل مستوى التسجيل

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### ❌ "لم يتم العثور على Chrome"
```bash
# تثبيت Chrome من الموقع الرسمي
https://www.google.com/chrome/
```

#### ❌ "خطأ في مراقبة الأحداث"
- **Windows**: تشغيل كمدير
- **macOS**: السماح بالوصول في إعدادات الخصوصية
- **Linux**: تثبيت `python3-xlib`

#### ❌ "فشل في تحميل البيانات"
- تأكد من صحة تنسيق الملف
- تحقق من الترميز (UTF-8 مستحسن)
- تأكد من عدم فتح الملف في برنامج آخر

#### ❌ "التشغيل التلقائي لا يعمل"
- تأكد من وجود تسجيل محفوظ
- تحقق من تحميل البيانات
- تأكد من عدم تغيير الموقع

### تشخيص متقدم
```bash
# تشغيل مع سجلات مفصلة
python smart_recorder_system.py --debug

# فحص ملفات السجلات
cat logs/smart_recorder_*.log
```

---

## 📊 أمثلة عملية

### مثال 1: تعبئة نموذج تسجيل
```
الهدف: تسجيل 1000 مستخدم جديد
البيانات: ملف Excel يحتوي على (الاسم، الإيميل، الهاتف)
الخطوات:
1. سجل تعبئة مستخدم واحد
2. حمل ملف الـ 1000 مستخدم
3. شغل تلقائياً
النتيجة: توفير 8+ ساعات عمل
```

### مثال 2: إدخال بيانات منتجات
```
الهدف: إضافة 500 منتج لمتجر إلكتروني
البيانات: CSV يحتوي على (اسم المنتج، السعر، الوصف، الفئة)
الميزة: التعامل مع القوائم المنسدلة والحقول المتعددة
النتيجة: دقة 98% وسرعة 10x
```

### مثال 3: تحديث بيانات موظفين
```
الهدف: تحديث معلومات 200 موظف
التحدي: نموذج متعدد الصفحات
الحل: تسجيل التنقل بين الصفحات
النتيجة: أتمتة كاملة للعملية
```

---

## 🛡️ الأمان والخصوصية

### حماية البيانات
- **التشفير**: إمكانية تشفير التسجيلات الحساسة
- **التنظيف**: حذف البيانات المؤقتة تلقائياً
- **الصلاحيات**: فحص الصلاحيات قبل التشغيل

### الاستخدام الآمن
- **فحص المواقع**: تجنب المواقع المشبوهة
- **النسخ الاحتياطي**: احتفظ بنسخة من بياناتك
- **الاختبار**: اختبر على بيانات قليلة أولاً

### إعدادات الخصوصية
```python
# في smart_config.py
SECURITY_CONFIG = {
    "safe_mode": True,           # وضع آمن
    "validate_inputs": True,     # فحص المدخلات
    "encrypt_recordings": True   # تشفير التسجيلات
}
```

---

## 🔮 الميزات القادمة

### الإصدار 1.1
- [ ] دعم المواقع متعددة اللغات
- [ ] تحسين التعرف على العناصر الديناميكية
- [ ] واجهة مستخدم محسنة
- [ ] تقارير مفصلة عن الأداء

### الإصدار 1.2
- [ ] دعم تطبيقات سطح المكتب
- [ ] تكامل مع APIs
- [ ] ذكاء اصطناعي متقدم
- [ ] مشاركة التسجيلات بين المستخدمين

### الإصدار 2.0
- [ ] واجهة ويب
- [ ] معالجة سحابية
- [ ] تعلم آلي متقدم
- [ ] دعم الفرق والمؤسسات

---

## 🤝 المساهمة والدعم

### الإبلاغ عن مشاكل
- استخدم نظام Issues في GitHub
- أرفق ملفات السجلات
- وصف خطوات إعادة إنتاج المشكلة

### طلب ميزات جديدة
- اشرح الحاجة للميزة
- قدم أمثلة عملية
- ناقش التنفيذ المقترح

### المساهمة في الكود
```bash
# Fork المشروع
git fork <repository-url>

# إنشاء فرع جديد
git checkout -b feature/new-feature

# إضافة التغييرات
git commit -m "إضافة ميزة جديدة"

# إرسال Pull Request
git push origin feature/new-feature
```

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

- **Selenium**: للتحكم في المتصفح
- **pynput**: لمراقبة أحداث النظام  
- **pyautogui**: للتحكم في الماوس والكيبورد
- **OpenCV**: للرؤية الحاسوبية
- **pandas**: لمعالجة البيانات

---

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://smart-system.com
- **التوثيق**: https://docs.smart-system.com

---

**🎬 نظام التسجيل الذكي - حيث تلتقي البساطة بالقوة!**
