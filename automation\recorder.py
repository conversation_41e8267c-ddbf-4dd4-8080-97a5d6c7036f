# -*- coding: utf-8 -*-
"""
مسجل الإجراءات - لتسجيل تفاعلات المستخدم مع الموقع
"""

import json
import time
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import WebDriverException
from config import RECORDING_CONFIG, SESSIONS_DIR
from utils.logger import logger


class ActionRecorder:
    """فئة تسجيل الإجراءات"""
    
    def __init__(self, web_driver):
        self.web_driver = web_driver
        self.recorded_actions = []
        self.is_recording = False
        self.current_session = None
        self.recording_count = 0
        
    def start_recording(self, session_name=None):
        """بدء تسجيل الإجراءات"""
        if session_name is None:
            session_name = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = session_name
        self.recorded_actions = []
        self.is_recording = True
        self.recording_count += 1
        
        logger.info(f"بدء التسجيل: {session_name} (التسجيل رقم {self.recording_count})")
        
        # تسجيل معلومات الجلسة
        session_info = {
            "session_name": session_name,
            "start_time": datetime.now().isoformat(),
            "recording_number": self.recording_count,
            "initial_url": self.web_driver.get_current_url(),
            "page_title": self.web_driver.get_page_title()
        }
        
        self.recorded_actions.append({
            "type": "session_start",
            "timestamp": time.time(),
            "data": session_info
        })
    
    def stop_recording(self):
        """إيقاف تسجيل الإجراءات"""
        if not self.is_recording:
            return False
        
        self.is_recording = False
        
        # تسجيل نهاية الجلسة
        self.recorded_actions.append({
            "type": "session_end",
            "timestamp": time.time(),
            "data": {
                "end_time": datetime.now().isoformat(),
                "total_actions": len(self.recorded_actions),
                "final_url": self.web_driver.get_current_url()
            }
        })
        
        logger.info(f"تم إيقاف التسجيل: {len(self.recorded_actions)} إجراء مسجل")
        return True
    
    def record_click(self, element):
        """تسجيل النقر على عنصر"""
        if not self.is_recording:
            return
        
        try:
            # الحصول على معلومات العنصر
            element_info = self._get_element_info(element)
            
            action = {
                "type": "click",
                "timestamp": time.time(),
                "element": element_info,
                "page_url": self.web_driver.get_current_url()
            }
            
            self.recorded_actions.append(action)
            
            # أخذ لقطة شاشة إذا كان مفعلاً
            if RECORDING_CONFIG["screenshot_on_action"]:
                screenshot_path = self._take_action_screenshot("click")
                action["screenshot"] = screenshot_path
            
            logger.debug(f"تم تسجيل النقر على: {element_info.get('tag_name', 'unknown')}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل النقر: {str(e)}")
    
    def record_input(self, element, text):
        """تسجيل إدخال النص"""
        if not self.is_recording:
            return
        
        try:
            element_info = self._get_element_info(element)
            
            action = {
                "type": "input",
                "timestamp": time.time(),
                "element": element_info,
                "text": text,
                "page_url": self.web_driver.get_current_url()
            }
            
            self.recorded_actions.append(action)
            
            if RECORDING_CONFIG["screenshot_on_action"]:
                screenshot_path = self._take_action_screenshot("input")
                action["screenshot"] = screenshot_path
            
            logger.debug(f"تم تسجيل الإدخال في: {element_info.get('tag_name', 'unknown')}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الإدخال: {str(e)}")
    
    def record_select(self, element, value):
        """تسجيل اختيار من قائمة منسدلة"""
        if not self.is_recording:
            return
        
        try:
            element_info = self._get_element_info(element)
            
            action = {
                "type": "select",
                "timestamp": time.time(),
                "element": element_info,
                "selected_value": value,
                "page_url": self.web_driver.get_current_url()
            }
            
            self.recorded_actions.append(action)
            
            if RECORDING_CONFIG["screenshot_on_action"]:
                screenshot_path = self._take_action_screenshot("select")
                action["screenshot"] = screenshot_path
            
            logger.debug(f"تم تسجيل الاختيار: {value}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الاختيار: {str(e)}")
    
    def _get_element_info(self, element):
        """الحصول على معلومات العنصر"""
        try:
            info = {
                "tag_name": element.tag_name,
                "id": element.get_attribute("id"),
                "name": element.get_attribute("name"),
                "class": element.get_attribute("class"),
                "type": element.get_attribute("type"),
                "placeholder": element.get_attribute("placeholder"),
                "text": element.text,
                "xpath": self._get_element_xpath(element)
            }
            
            # إزالة القيم الفارغة
            return {k: v for k, v in info.items() if v}
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات العنصر: {str(e)}")
            return {}
    
    def _get_element_xpath(self, element):
        """الحصول على XPath للعنصر"""
        try:
            return self.web_driver.driver.execute_script(
                "function getXPath(element) {"
                "if (element.id !== '') return '//*[@id=\"' + element.id + '\"]';"
                "if (element === document.body) return '/html/body';"
                "var ix = 0;"
                "var siblings = element.parentNode.childNodes;"
                "for (var i = 0; i < siblings.length; i++) {"
                "var sibling = siblings[i];"
                "if (sibling === element) return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';"
                "if (sibling.nodeType === 1 && sibling.tagName === element.tagName) ix++;"
                "}"
                "}"
                "return getXPath(arguments[0]);", element
            )
        except:
            return None
    
    def _take_action_screenshot(self, action_type):
        """أخذ لقطة شاشة للإجراء"""
        try:
            timestamp = int(time.time())
            filename = f"{self.current_session}_{action_type}_{timestamp}.png"
            return self.web_driver.take_screenshot(filename)
        except:
            return None
    
    def save_recording(self, filename=None):
        """حفظ التسجيل في ملف"""
        if not self.recorded_actions:
            logger.warning("لا توجد إجراءات مسجلة للحفظ")
            return False
        
        try:
            if filename is None:
                filename = f"{self.current_session}.json"
            
            file_path = SESSIONS_DIR / filename
            
            recording_data = {
                "session_info": {
                    "name": self.current_session,
                    "recording_number": self.recording_count,
                    "total_actions": len(self.recorded_actions),
                    "created_at": datetime.now().isoformat()
                },
                "actions": self.recorded_actions
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(recording_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ التسجيل: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التسجيل: {str(e)}")
            return False
    
    def get_recorded_actions_count(self):
        """الحصول على عدد الإجراءات المسجلة"""
        return len(self.recorded_actions)
    
    def clear_recording(self):
        """مسح التسجيل الحالي"""
        self.recorded_actions = []
        self.is_recording = False
        logger.info("تم مسح التسجيل الحالي")
