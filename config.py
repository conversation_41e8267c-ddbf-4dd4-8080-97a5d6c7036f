# -*- coding: utf-8 -*-
"""
إعدادات البرنامج الأساسية
"""

import os
from pathlib import Path

# مسارات المشروع
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
SESSIONS_DIR = PROJECT_ROOT / "sessions"
LOGS_DIR = PROJECT_ROOT / "logs"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, SESSIONS_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات المتصفح
BROWSER_CONFIG = {
    "default_browser": "chrome",
    "headless": False,
    "window_size": (1200, 800),
    "implicit_wait": 10,
    "page_load_timeout": 30
}

# إعدادات التسجيل
RECORDING_CONFIG = {
    "max_recordings": 3,
    "delay_between_actions": 0.5,
    "screenshot_on_action": True,
    "record_mouse_movements": False
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    "window_title": "برنامج تعبئة البيانات التلقائي",
    "window_size": "1400x800",
    "theme": "default",
    "font_family": "Arial",
    "font_size": 10
}

# إعدادات البيانات
DATA_CONFIG = {
    "supported_formats": [".xlsx", ".xls", ".csv"],
    "max_rows": 10000,
    "encoding": "utf-8"
}

# إعدادات السجلات
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_name": "auto_form_filler.log"
}
