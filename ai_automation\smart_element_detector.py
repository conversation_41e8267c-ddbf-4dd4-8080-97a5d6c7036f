# -*- coding: utf-8 -*-
"""
نظام التعرف الذكي على العناصر
يستخدم الذكاء الاصطناعي والرؤية الحاسوبية لتحديد العناصر التفاعلية
"""

import cv2
import numpy as np
import time
import json
from typing import List, Dict, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import base64
from PIL import Image
import io
from utils.logger import logger

class SmartElementDetector:
    """كاشف العناصر الذكي"""
    
    def __init__(self, web_driver):
        self.web_driver = web_driver
        self.detected_elements = []
        self.element_patterns = {}
        self.form_structure = {}
        
    def analyze_page_structure(self) -> Dict:
        """تحليل هيكل الصفحة بذكاء"""
        try:
            logger.info("بدء تحليل هيكل الصفحة...")
            
            # الحصول على لقطة شاشة للصفحة
            screenshot = self._take_page_screenshot()
            
            # تحليل DOM
            dom_analysis = self._analyze_dom_structure()
            
            # تحليل بصري للعناصر
            visual_analysis = self._analyze_visual_elements(screenshot)
            
            # دمج التحليلات
            combined_analysis = self._combine_analyses(dom_analysis, visual_analysis)
            
            self.form_structure = combined_analysis
            logger.info(f"تم تحليل {len(combined_analysis.get('elements', []))} عنصر")
            
            return combined_analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل هيكل الصفحة: {str(e)}")
            return {}
    
    def _take_page_screenshot(self) -> np.ndarray:
        """أخذ لقطة شاشة للصفحة"""
        try:
            # أخذ لقطة شاشة بـ Selenium
            screenshot_base64 = self.web_driver.get_screenshot_as_base64()
            
            # تحويل إلى صورة
            screenshot_data = base64.b64decode(screenshot_base64)
            image = Image.open(io.BytesIO(screenshot_data))
            
            # تحويل إلى OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            return opencv_image
            
        except Exception as e:
            logger.error(f"خطأ في أخذ لقطة الشاشة: {str(e)}")
            return np.array([])
    
    def _analyze_dom_structure(self) -> Dict:
        """تحليل هيكل DOM"""
        try:
            elements_data = {
                'forms': [],
                'inputs': [],
                'buttons': [],
                'selects': [],
                'textareas': [],
                'links': []
            }
            
            # البحث عن النماذج
            forms = self.web_driver.find_elements(By.TAG_NAME, "form")
            for form in forms:
                form_info = self._extract_element_info(form, 'form')
                elements_data['forms'].append(form_info)
            
            # البحث عن حقول الإدخال
            inputs = self.web_driver.find_elements(By.TAG_NAME, "input")
            for input_elem in inputs:
                input_info = self._extract_element_info(input_elem, 'input')
                elements_data['inputs'].append(input_info)
            
            # البحث عن الأزرار
            buttons = self.web_driver.find_elements(By.TAG_NAME, "button")
            submit_inputs = self.web_driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
            
            for button in buttons + submit_inputs:
                button_info = self._extract_element_info(button, 'button')
                elements_data['buttons'].append(button_info)
            
            # البحث عن القوائم المنسدلة
            selects = self.web_driver.find_elements(By.TAG_NAME, "select")
            for select in selects:
                select_info = self._extract_element_info(select, 'select')
                elements_data['selects'].append(select_info)
            
            # البحث عن مناطق النص
            textareas = self.web_driver.find_elements(By.TAG_NAME, "textarea")
            for textarea in textareas:
                textarea_info = self._extract_element_info(textarea, 'textarea')
                elements_data['textareas'].append(textarea_info)
            
            return elements_data
            
        except Exception as e:
            logger.error(f"خطأ في تحليل DOM: {str(e)}")
            return {}
    
    def _extract_element_info(self, element, element_type: str) -> Dict:
        """استخراج معلومات العنصر"""
        try:
            # الحصول على الموقع والحجم
            location = element.location
            size = element.size
            
            # الحصول على الخصائص
            attributes = {}
            common_attrs = ['id', 'name', 'class', 'type', 'placeholder', 'value', 'title', 'aria-label']
            
            for attr in common_attrs:
                try:
                    value = element.get_attribute(attr)
                    if value:
                        attributes[attr] = value
                except:
                    pass
            
            # الحصول على النص
            text = ""
            try:
                text = element.text or element.get_attribute('textContent') or ""
            except:
                pass
            
            # إنشاء selector فريد
            selector = self._generate_unique_selector(element)
            
            element_info = {
                'type': element_type,
                'tag_name': element.tag_name,
                'location': location,
                'size': size,
                'attributes': attributes,
                'text': text.strip(),
                'selector': selector,
                'xpath': self._generate_xpath(element),
                'is_visible': element.is_displayed(),
                'is_enabled': element.is_enabled()
            }
            
            return element_info
            
        except Exception as e:
            logger.error(f"خطأ في استخراج معلومات العنصر: {str(e)}")
            return {}
    
    def _generate_unique_selector(self, element) -> str:
        """إنشاء selector فريد للعنصر"""
        try:
            # محاولة استخدام ID أولاً
            element_id = element.get_attribute('id')
            if element_id:
                return f"#{element_id}"
            
            # محاولة استخدام name
            name = element.get_attribute('name')
            if name:
                return f"[name='{name}']"
            
            # محاولة استخدام class فريدة
            class_name = element.get_attribute('class')
            if class_name:
                classes = class_name.split()
                for cls in classes:
                    elements_with_class = self.web_driver.find_elements(By.CLASS_NAME, cls)
                    if len(elements_with_class) == 1:
                        return f".{cls}"
            
            # استخدام xpath كحل أخير
            return self._generate_xpath(element)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء selector: {str(e)}")
            return ""
    
    def _generate_xpath(self, element) -> str:
        """إنشاء XPath للعنصر"""
        try:
            return self.web_driver.execute_script("""
                function getXPath(element) {
                    if (element.id !== '') {
                        return '//*[@id="' + element.id + '"]';
                    }
                    if (element === document.body) {
                        return '/html/body';
                    }
                    
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }
                return getXPath(arguments[0]);
            """, element)
        except:
            return ""
    
    def _analyze_visual_elements(self, screenshot: np.ndarray) -> Dict:
        """تحليل بصري للعناصر"""
        try:
            if screenshot.size == 0:
                return {}
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            # كشف الحواف
            edges = cv2.Canny(gray, 50, 150)
            
            # البحث عن المستطيلات (حقول الإدخال المحتملة)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            potential_fields = []
            for contour in contours:
                # تقريب الشكل
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # إذا كان مستطيل تقريباً
                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # تصفية بناءً على الحجم (حقول الإدخال عادة لها أبعاد معينة)
                    if 50 < w < 500 and 20 < h < 100:
                        potential_fields.append({
                            'x': x, 'y': y, 'width': w, 'height': h,
                            'area': w * h,
                            'aspect_ratio': w / h
                        })
            
            return {
                'potential_input_fields': potential_fields,
                'total_detected': len(potential_fields)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل البصري: {str(e)}")
            return {}
    
    def _combine_analyses(self, dom_analysis: Dict, visual_analysis: Dict) -> Dict:
        """دمج تحليلات DOM والرؤية الحاسوبية"""
        try:
            combined = {
                'timestamp': time.time(),
                'page_url': self.web_driver.current_url,
                'page_title': self.web_driver.title,
                'elements': [],
                'forms_count': len(dom_analysis.get('forms', [])),
                'inputs_count': len(dom_analysis.get('inputs', [])),
                'buttons_count': len(dom_analysis.get('buttons', [])),
                'visual_fields_detected': visual_analysis.get('total_detected', 0)
            }
            
            # إضافة جميع العناصر من DOM
            for category, elements in dom_analysis.items():
                for element in elements:
                    element['detection_method'] = 'dom'
                    element['confidence'] = 0.9  # ثقة عالية للعناصر المكتشفة بـ DOM
                    combined['elements'].append(element)
            
            # تحسين الثقة بناءً على التحليل البصري
            self._enhance_confidence_with_visual_data(combined['elements'], visual_analysis)
            
            return combined
            
        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {str(e)}")
            return {}
    
    def _enhance_confidence_with_visual_data(self, elements: List[Dict], visual_data: Dict):
        """تحسين مستوى الثقة بناءً على البيانات البصرية"""
        try:
            visual_fields = visual_data.get('potential_input_fields', [])
            
            for element in elements:
                if element['type'] in ['input', 'textarea']:
                    element_location = element.get('location', {})
                    element_size = element.get('size', {})
                    
                    # البحث عن تطابق بصري
                    for visual_field in visual_fields:
                        # حساب التداخل
                        overlap = self._calculate_overlap(
                            element_location, element_size, visual_field
                        )
                        
                        if overlap > 0.7:  # تداخل عالي
                            element['confidence'] = min(element['confidence'] + 0.1, 1.0)
                            element['visual_match'] = True
                            break
                    else:
                        element['visual_match'] = False
                        
        except Exception as e:
            logger.error(f"خطأ في تحسين الثقة: {str(e)}")
    
    def _calculate_overlap(self, location: Dict, size: Dict, visual_field: Dict) -> float:
        """حساب نسبة التداخل بين العنصر والحقل البصري"""
        try:
            # إحداثيات العنصر من DOM
            x1, y1 = location.get('x', 0), location.get('y', 0)
            w1, h1 = size.get('width', 0), size.get('height', 0)
            
            # إحداثيات الحقل البصري
            x2, y2 = visual_field['x'], visual_field['y']
            w2, h2 = visual_field['width'], visual_field['height']
            
            # حساب التداخل
            overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
            overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
            overlap_area = overlap_x * overlap_y
            
            # حساب المساحة الإجمالية
            total_area = w1 * h1 + w2 * h2 - overlap_area
            
            if total_area > 0:
                return overlap_area / total_area
            return 0
            
        except Exception as e:
            logger.error(f"خطأ في حساب التداخل: {str(e)}")
            return 0
    
    def get_interactive_elements(self) -> List[Dict]:
        """الحصول على العناصر التفاعلية المكتشفة"""
        if not self.form_structure:
            self.analyze_page_structure()
        
        interactive_elements = []
        for element in self.form_structure.get('elements', []):
            if element['type'] in ['input', 'button', 'select', 'textarea'] and element['is_visible']:
                interactive_elements.append(element)
        
        # ترتيب حسب مستوى الثقة
        interactive_elements.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        return interactive_elements
    
    def find_element_by_smart_selector(self, element_info: Dict):
        """العثور على العنصر باستخدام أذكى selector متاح"""
        try:
            # محاولة الطرق المختلفة بترتيب الأولوية
            selectors_to_try = [
                (By.CSS_SELECTOR, element_info.get('selector', '')),
                (By.XPATH, element_info.get('xpath', '')),
                (By.ID, element_info.get('attributes', {}).get('id', '')),
                (By.NAME, element_info.get('attributes', {}).get('name', '')),
            ]
            
            for by_method, selector_value in selectors_to_try:
                if selector_value:
                    try:
                        element = WebDriverWait(self.web_driver, 2).until(
                            EC.presence_of_element_located((by_method, selector_value))
                        )
                        if element.is_displayed():
                            return element
                    except (TimeoutException, NoSuchElementException):
                        continue
            
            logger.warning(f"لم يتم العثور على العنصر: {element_info.get('type', 'unknown')}")
            return None
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن العنصر: {str(e)}")
            return None
