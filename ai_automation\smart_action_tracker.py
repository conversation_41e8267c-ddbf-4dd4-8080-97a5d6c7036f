# -*- coding: utf-8 -*-
"""
نظام تتبع الإجراءات الذكي
يراقب ويتعلم من إجراءات المستخدم في الوقت الفعلي
"""

import time
import json
import threading
from typing import List, Dict, Optional, Callable
from datetime import datetime
from pynput import mouse, keyboard
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from ai_automation.smart_element_detector import SmartElementDetector
from utils.logger import logger

class SmartActionTracker:
    """متتبع الإجراءات الذكي"""
    
    def __init__(self, web_driver):
        self.web_driver = web_driver
        self.element_detector = SmartElementDetector(web_driver)
        
        # حالة التتبع
        self.is_tracking = False
        self.tracked_actions = []
        self.current_session = None
        
        # نمط التعلم
        self.learned_pattern = {}
        self.action_sequence = []
        self.data_mapping = {}
        
        # مراقبة الأحداث
        self.mouse_listener = None
        self.keyboard_listener = None
        self.last_mouse_position = (0, 0)
        self.last_click_time = 0
        self.typing_buffer = ""
        self.typing_timeout = 1.0  # ثانية
        
        # callbacks
        self.on_action_detected = None
        self.on_pattern_learned = None
        
    def start_tracking(self, session_name: str = None) -> bool:
        """بدء تتبع الإجراءات"""
        try:
            if self.is_tracking:
                logger.warning("التتبع قيد التشغيل بالفعل")
                return False
            
            if session_name is None:
                session_name = f"smart_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.current_session = session_name
            self.tracked_actions = []
            self.action_sequence = []
            
            # تحليل هيكل الصفحة أولاً
            logger.info("تحليل هيكل الصفحة...")
            page_structure = self.element_detector.analyze_page_structure()
            
            # بدء مراقبة الأحداث
            self._start_event_monitoring()
            
            self.is_tracking = True
            logger.info(f"بدء التتبع الذكي: {session_name}")
            
            # تسجيل بداية الجلسة
            self._record_action({
                'type': 'session_start',
                'timestamp': time.time(),
                'session_name': session_name,
                'page_url': self.web_driver.current_url,
                'page_title': self.web_driver.title,
                'page_structure': page_structure
            })
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء التتبع: {str(e)}")
            return False
    
    def stop_tracking(self) -> Dict:
        """إيقاف التتبع وتحليل النمط"""
        try:
            if not self.is_tracking:
                logger.warning("التتبع غير نشط")
                return {}
            
            self.is_tracking = False
            
            # إيقاف مراقبة الأحداث
            self._stop_event_monitoring()
            
            # تسجيل نهاية الجلسة
            self._record_action({
                'type': 'session_end',
                'timestamp': time.time(),
                'total_actions': len(self.tracked_actions)
            })
            
            # تحليل النمط المتعلم
            learned_pattern = self._analyze_learned_pattern()
            
            logger.info(f"تم إيقاف التتبع. تم تسجيل {len(self.tracked_actions)} إجراء")
            
            if self.on_pattern_learned:
                self.on_pattern_learned(learned_pattern)
            
            return learned_pattern
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف التتبع: {str(e)}")
            return {}
    
    def _start_event_monitoring(self):
        """بدء مراقبة أحداث الماوس ولوحة المفاتيح"""
        try:
            # مراقبة الماوس
            self.mouse_listener = mouse.Listener(
                on_click=self._on_mouse_click,
                on_move=self._on_mouse_move
            )
            self.mouse_listener.start()
            
            # مراقبة لوحة المفاتيح
            self.keyboard_listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.keyboard_listener.start()
            
            logger.debug("بدء مراقبة أحداث الإدخال")
            
        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة الأحداث: {str(e)}")
    
    def _stop_event_monitoring(self):
        """إيقاف مراقبة الأحداث"""
        try:
            if self.mouse_listener:
                self.mouse_listener.stop()
                self.mouse_listener = None
            
            if self.keyboard_listener:
                self.keyboard_listener.stop()
                self.keyboard_listener = None
            
            logger.debug("تم إيقاف مراقبة الأحداث")
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف مراقبة الأحداث: {str(e)}")
    
    def _on_mouse_click(self, x, y, button, pressed):
        """معالج النقر بالماوس"""
        if not self.is_tracking or not pressed:
            return
        
        try:
            current_time = time.time()
            
            # تجنب النقرات المتكررة السريعة
            if current_time - self.last_click_time < 0.1:
                return
            
            self.last_click_time = current_time
            
            # تحديد العنصر المنقور عليه
            clicked_element = self._find_element_at_position(x, y)
            
            if clicked_element:
                action_data = {
                    'type': 'click',
                    'timestamp': current_time,
                    'mouse_position': (x, y),
                    'button': str(button),
                    'element_info': clicked_element,
                    'page_url': self.web_driver.current_url
                }
                
                self._record_action(action_data)
                logger.debug(f"تم تسجيل نقرة على: {clicked_element.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة النقر: {str(e)}")
    
    def _on_mouse_move(self, x, y):
        """معالج حركة الماوس"""
        self.last_mouse_position = (x, y)
    
    def _on_key_press(self, key):
        """معالج الضغط على المفاتيح"""
        if not self.is_tracking:
            return
        
        try:
            # تجميع الكتابة في buffer
            if hasattr(key, 'char') and key.char:
                self.typing_buffer += key.char
                
                # بدء timer لمعالجة النص المكتوب
                threading.Timer(self.typing_timeout, self._process_typing_buffer).start()
            
            elif key == keyboard.Key.enter:
                self._process_typing_buffer()
                self._record_special_key('enter')
            
            elif key == keyboard.Key.tab:
                self._process_typing_buffer()
                self._record_special_key('tab')
                
        except Exception as e:
            logger.error(f"خطأ في معالجة الضغط على المفتاح: {str(e)}")
    
    def _on_key_release(self, key):
        """معالج تحرير المفاتيح"""
        pass
    
    def _process_typing_buffer(self):
        """معالجة النص المكتوب"""
        if not self.typing_buffer.strip():
            return
        
        try:
            # العثور على العنصر النشط
            active_element = self._find_active_element()
            
            if active_element:
                action_data = {
                    'type': 'input',
                    'timestamp': time.time(),
                    'text': self.typing_buffer,
                    'element_info': active_element,
                    'page_url': self.web_driver.current_url
                }
                
                self._record_action(action_data)
                logger.debug(f"تم تسجيل إدخال نص: {self.typing_buffer[:20]}...")
            
            self.typing_buffer = ""
            
        except Exception as e:
            logger.error(f"خطأ في معالجة النص المكتوب: {str(e)}")
    
    def _record_special_key(self, key_name: str):
        """تسجيل المفاتيح الخاصة"""
        try:
            action_data = {
                'type': 'special_key',
                'timestamp': time.time(),
                'key': key_name,
                'page_url': self.web_driver.current_url
            }
            
            self._record_action(action_data)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل المفتاح الخاص: {str(e)}")
    
    def _find_element_at_position(self, x, y) -> Optional[Dict]:
        """العثور على العنصر في الموقع المحدد"""
        try:
            # استخدام JavaScript للعثور على العنصر
            script = f"""
                var element = document.elementFromPoint({x}, {y});
                if (element) {{
                    return {{
                        tagName: element.tagName,
                        id: element.id,
                        className: element.className,
                        name: element.name,
                        type: element.type,
                        value: element.value,
                        placeholder: element.placeholder,
                        textContent: element.textContent
                    }};
                }}
                return null;
            """
            
            element_data = self.web_driver.execute_script(script)
            
            if element_data:
                # إثراء البيانات بمعلومات إضافية
                enhanced_data = self._enhance_element_data(element_data, x, y)
                return enhanced_data
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في العثور على العنصر: {str(e)}")
            return None
    
    def _find_active_element(self) -> Optional[Dict]:
        """العثور على العنصر النشط حالياً"""
        try:
            script = """
                var activeElement = document.activeElement;
                if (activeElement && activeElement.tagName !== 'BODY') {
                    return {
                        tagName: activeElement.tagName,
                        id: activeElement.id,
                        className: activeElement.className,
                        name: activeElement.name,
                        type: activeElement.type,
                        value: activeElement.value,
                        placeholder: activeElement.placeholder
                    };
                }
                return null;
            """
            
            element_data = self.web_driver.execute_script(script)
            
            if element_data:
                return self._enhance_element_data(element_data)
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في العثور على العنصر النشط: {str(e)}")
            return None
    
    def _enhance_element_data(self, element_data: Dict, x: int = None, y: int = None) -> Dict:
        """إثراء بيانات العنصر بمعلومات إضافية"""
        try:
            enhanced = element_data.copy()
            enhanced['timestamp'] = time.time()
            
            if x is not None and y is not None:
                enhanced['click_position'] = (x, y)
            
            # تحديد نوع العنصر
            tag_name = element_data.get('tagName', '').lower()
            element_type = element_data.get('type', '').lower()
            
            if tag_name == 'input':
                if element_type in ['text', 'email', 'password', 'tel', 'url']:
                    enhanced['element_type'] = 'text_input'
                elif element_type in ['submit', 'button']:
                    enhanced['element_type'] = 'button'
                elif element_type == 'checkbox':
                    enhanced['element_type'] = 'checkbox'
                elif element_type == 'radio':
                    enhanced['element_type'] = 'radio'
                else:
                    enhanced['element_type'] = 'input'
            elif tag_name == 'button':
                enhanced['element_type'] = 'button'
            elif tag_name == 'select':
                enhanced['element_type'] = 'select'
            elif tag_name == 'textarea':
                enhanced['element_type'] = 'textarea'
            else:
                enhanced['element_type'] = tag_name
            
            # إنشاء selector
            enhanced['selector'] = self._generate_selector(element_data)
            
            return enhanced
            
        except Exception as e:
            logger.error(f"خطأ في إثراء بيانات العنصر: {str(e)}")
            return element_data
    
    def _generate_selector(self, element_data: Dict) -> str:
        """إنشاء selector للعنصر"""
        try:
            # أولوية للـ ID
            if element_data.get('id'):
                return f"#{element_data['id']}"
            
            # ثم name
            if element_data.get('name'):
                return f"[name='{element_data['name']}']"
            
            # ثم type للـ input
            tag_name = element_data.get('tagName', '').lower()
            if tag_name == 'input' and element_data.get('type'):
                return f"input[type='{element_data['type']}']"
            
            # أخيراً tag name
            return tag_name
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء selector: {str(e)}")
            return ""
    
    def _record_action(self, action_data: Dict):
        """تسجيل الإجراء"""
        try:
            self.tracked_actions.append(action_data)
            self.action_sequence.append(action_data)
            
            # استدعاء callback إذا كان موجوداً
            if self.on_action_detected:
                self.on_action_detected(action_data)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الإجراء: {str(e)}")
    
    def _analyze_learned_pattern(self) -> Dict:
        """تحليل النمط المتعلم من الإجراءات"""
        try:
            if not self.action_sequence:
                return {}
            
            # تصفية الإجراءات المهمة فقط
            important_actions = [
                action for action in self.action_sequence
                if action['type'] in ['click', 'input', 'special_key']
            ]
            
            # تحليل تسلسل الإجراءات
            pattern = {
                'session_name': self.current_session,
                'total_actions': len(important_actions),
                'action_sequence': important_actions,
                'form_fields': self._extract_form_fields(important_actions),
                'navigation_pattern': self._extract_navigation_pattern(important_actions),
                'data_requirements': self._analyze_data_requirements(important_actions),
                'confidence_score': self._calculate_pattern_confidence(important_actions)
            }
            
            self.learned_pattern = pattern
            return pattern
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النمط: {str(e)}")
            return {}
    
    def _extract_form_fields(self, actions: List[Dict]) -> List[Dict]:
        """استخراج حقول النموذج من الإجراءات"""
        form_fields = []
        
        for action in actions:
            if action['type'] == 'input':
                element_info = action.get('element_info', {})
                field_info = {
                    'selector': element_info.get('selector', ''),
                    'element_type': element_info.get('element_type', ''),
                    'name': element_info.get('name', ''),
                    'placeholder': element_info.get('placeholder', ''),
                    'sample_value': action.get('text', ''),
                    'order': len(form_fields) + 1
                }
                form_fields.append(field_info)
        
        return form_fields
    
    def _extract_navigation_pattern(self, actions: List[Dict]) -> List[Dict]:
        """استخراج نمط التنقل"""
        navigation = []
        
        for action in actions:
            if action['type'] == 'click':
                element_info = action.get('element_info', {})
                if element_info.get('element_type') == 'button':
                    nav_step = {
                        'action': 'click',
                        'selector': element_info.get('selector', ''),
                        'element_type': 'button',
                        'text': element_info.get('textContent', ''),
                        'order': len(navigation) + 1
                    }
                    navigation.append(nav_step)
        
        return navigation
    
    def _analyze_data_requirements(self, actions: List[Dict]) -> Dict:
        """تحليل متطلبات البيانات"""
        requirements = {
            'required_fields': [],
            'field_types': {},
            'validation_patterns': {}
        }
        
        for action in actions:
            if action['type'] == 'input':
                element_info = action.get('element_info', {})
                field_name = element_info.get('name') or element_info.get('id') or f"field_{len(requirements['required_fields'])}"
                
                requirements['required_fields'].append(field_name)
                requirements['field_types'][field_name] = element_info.get('element_type', 'text')
                
                # تحليل نمط البيانات
                sample_value = action.get('text', '')
                if sample_value:
                    pattern = self._detect_data_pattern(sample_value)
                    requirements['validation_patterns'][field_name] = pattern
        
        return requirements
    
    def _detect_data_pattern(self, value: str) -> str:
        """كشف نمط البيانات"""
        import re
        
        if re.match(r'^[a-zA-Z\s]+$', value):
            return 'text'
        elif re.match(r'^\d+$', value):
            return 'number'
        elif re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', value):
            return 'email'
        elif re.match(r'^\+?\d{10,15}$', value.replace(' ', '').replace('-', '')):
            return 'phone'
        else:
            return 'mixed'
    
    def _calculate_pattern_confidence(self, actions: List[Dict]) -> float:
        """حساب مستوى الثقة في النمط"""
        if not actions:
            return 0.0
        
        # عوامل الثقة
        has_form_inputs = any(action['type'] == 'input' for action in actions)
        has_submit_action = any(
            action['type'] == 'click' and 
            action.get('element_info', {}).get('element_type') == 'button'
            for action in actions
        )
        
        confidence = 0.5  # قاعدة أساسية
        
        if has_form_inputs:
            confidence += 0.3
        
        if has_submit_action:
            confidence += 0.2
        
        # تقليل الثقة إذا كان عدد الإجراءات قليل جداً
        if len(actions) < 3:
            confidence *= 0.7
        
        return min(confidence, 1.0)
    
    def get_learned_pattern(self) -> Dict:
        """الحصول على النمط المتعلم"""
        return self.learned_pattern.copy()
    
    def save_pattern(self, filename: str = None) -> str:
        """حفظ النمط المتعلم"""
        try:
            if not self.learned_pattern:
                logger.warning("لا يوجد نمط متعلم للحفظ")
                return ""
            
            if filename is None:
                filename = f"smart_pattern_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            from config import SESSIONS_DIR
            file_path = SESSIONS_DIR / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.learned_pattern, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ النمط: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في حفظ النمط: {str(e)}")
            return ""
