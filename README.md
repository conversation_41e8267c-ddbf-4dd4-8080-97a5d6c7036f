# برنامج تعبئة البيانات التلقائي

برنامج Python لأتمتة تعبئة النماذج الإلكترونية باستخدام Selenium وواجهة مستخدم بـ Tkinter.

## الميزات

- **واجهة مستخدم سهلة الاستخدام** مع عرض البيانات على اليمين والموقع على اليسار
- **تسجيل الإجراءات** - سجل تفاعلاتك مع الموقع 3 مرات لتعلم النمط
- **تعبئة تلقائية** - تكرار العملية تلقائياً لجميع البيانات
- **دعم ملفات متعددة** - Excel (.xlsx, .xls) و CSV
- **إدارة الجلسات** - حفظ واستعادة التسجيلات
- **مراقبة التقدم** - عرض حالة التعبئة والتقدم المحرز

## متطلبات النظام

- Python 3.7 أو أحدث
- Google Chrome (للتحكم في المتصفح)
- Windows 10/11 (تم الاختبار عليه)

## التثبيت

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd auto-form-filler
```

2. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

3. **تشغيل البرنامج:**
```bash
python main.py
```

## كيفية الاستخدام

### الخطوة 1: تحضير البيانات
1. أنشئ ملف Excel أو CSV يحتوي على البيانات المراد تعبئتها
2. تأكد من أن أسماء الأعمدة واضحة ومفهومة
3. احفظ الملف في مكان يسهل الوصول إليه

### الخطوة 2: تحميل البيانات
1. افتح البرنامج
2. اضغط على "تحميل ملف البيانات"
3. اختر ملف البيانات الخاص بك
4. ستظهر البيانات في الجدول على اليمين

### الخطوة 3: فتح الموقع
1. أدخل رابط الموقع في حقل "رابط الموقع"
2. اضغط على "فتح المتصفح"
3. انتقل إلى صفحة النموذج المراد تعبئته

### الخطوة 4: تسجيل الإجراءات (3 مرات)
1. اضغط على "بدء التسجيل"
2. قم بتعبئة النموذج يدوياً بالبيانات من الصف الأول
3. اضغط على "إيقاف التسجيل"
4. كرر العملية 3 مرات بنفس الخطوات

### الخطوة 5: التعبئة التلقائية
1. بعد إكمال 3 تسجيلات، سيتم تفعيل زر "بدء التعبئة التلقائية"
2. اضغط على الزر لبدء التعبئة التلقائية
3. راقب التقدم في شريط التقدم
4. سيتم تعبئة جميع البيانات تلقائياً

## هيكل المشروع

```
auto-form-filler/
├── main.py                 # الملف الرئيسي
├── config.py              # إعدادات البرنامج
├── requirements.txt       # المتطلبات
├── README.md             # دليل الاستخدام
├── gui/                  # واجهة المستخدم
│   ├── __init__.py
│   └── main_window.py    # النافذة الرئيسية
├── automation/           # نظام الأتمتة
│   ├── __init__.py
│   ├── web_driver.py     # إدارة المتصفح
│   ├── recorder.py       # تسجيل الإجراءات
│   └── player.py         # تشغيل الإجراءات
├── data_management/      # إدارة البيانات
│   ├── __init__.py
│   ├── data_manager.py   # إدارة ملفات البيانات
│   └── session_manager.py # إدارة الجلسات
├── utils/               # أدوات مساعدة
│   ├── __init__.py
│   └── logger.py        # نظام السجلات
├── data/               # مجلد البيانات
├── sessions/           # مجلد الجلسات المحفوظة
└── logs/              # مجلد السجلات
```

## نصائح للاستخدام

1. **تأكد من استقرار الاتصال بالإنترنت** أثناء التعبئة التلقائية
2. **لا تتفاعل مع المتصفح** أثناء التسجيل أو التعبئة التلقائية
3. **استخدم أسماء أعمدة واضحة** في ملف البيانات لتسهيل التطابق
4. **اختبر على بيانات قليلة أولاً** قبل تشغيل البرنامج على مجموعة كبيرة
5. **احتفظ بنسخة احتياطية** من بياناتك قبل البدء

## استكشاف الأخطاء

### المتصفح لا يفتح
- تأكد من تثبيت Google Chrome
- تحقق من اتصال الإنترنت
- أعد تشغيل البرنامج

### فشل في تحميل البيانات
- تأكد من صحة تنسيق الملف (Excel أو CSV)
- تحقق من وجود بيانات في الملف
- تأكد من عدم فتح الملف في برنامج آخر

### التعبئة التلقائية لا تعمل
- تأكد من إكمال 3 تسجيلات
- تحقق من أن الموقع لم يتغير
- أعد التسجيل إذا لزم الأمر

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى:
1. مراجعة ملفات السجلات في مجلد `logs/`
2. التأكد من اتباع جميع الخطوات بالترتيب الصحيح
3. إعادة تشغيل البرنامج في حالة حدوث مشاكل

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.
