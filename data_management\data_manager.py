# -*- coding: utf-8 -*-
"""
مدير البيانات - لقراءة وإدارة ملفات البيانات
"""

import pandas as pd
import os
from pathlib import Path
from config import DATA_CONFIG
from utils.logger import logger


class DataManager:
    """فئة إدارة البيانات"""
    
    def __init__(self):
        self.data = None
        self.current_row = 0
        self.total_rows = 0
        self.file_path = None
        self.columns = []
    
    def load_data(self, file_path):
        """تحميل البيانات من ملف"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")
            
            if file_path.suffix not in DATA_CONFIG["supported_formats"]:
                raise ValueError(f"نوع الملف غير مدعوم: {file_path.suffix}")
            
            # قراءة البيانات حسب نوع الملف
            if file_path.suffix in ['.xlsx', '.xls']:
                self.data = pd.read_excel(file_path)
            elif file_path.suffix == '.csv':
                self.data = pd.read_csv(file_path, encoding=DATA_CONFIG["encoding"])
            
            # التحقق من حجم البيانات
            if len(self.data) > DATA_CONFIG["max_rows"]:
                logger.warning(f"عدد الصفوف ({len(self.data)}) يتجاوز الحد الأقصى ({DATA_CONFIG['max_rows']})")
            
            self.total_rows = len(self.data)
            self.columns = list(self.data.columns)
            self.file_path = file_path
            self.current_row = 0
            
            logger.info(f"تم تحميل البيانات بنجاح: {self.total_rows} صف، {len(self.columns)} عمود")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {str(e)}")
            return False
    
    def get_current_row_data(self):
        """الحصول على بيانات الصف الحالي"""
        if self.data is None or self.current_row >= self.total_rows:
            return None
        
        row_data = self.data.iloc[self.current_row].to_dict()
        return row_data
    
    def get_next_row_data(self):
        """الانتقال للصف التالي والحصول على بياناته"""
        if self.current_row < self.total_rows - 1:
            self.current_row += 1
            return self.get_current_row_data()
        return None
    
    def get_previous_row_data(self):
        """الانتقال للصف السابق والحصول على بياناته"""
        if self.current_row > 0:
            self.current_row -= 1
            return self.get_current_row_data()
        return None
    
    def reset_position(self):
        """إعادة تعيين الموضع للصف الأول"""
        self.current_row = 0
    
    def get_progress(self):
        """الحصول على نسبة التقدم"""
        if self.total_rows == 0:
            return 0
        return (self.current_row / self.total_rows) * 100
    
    def get_data_preview(self, rows=5):
        """الحصول على معاينة للبيانات"""
        if self.data is None:
            return None
        return self.data.head(rows)
    
    def get_column_names(self):
        """الحصول على أسماء الأعمدة"""
        return self.columns.copy() if self.columns else []
    
    def is_data_loaded(self):
        """التحقق من تحميل البيانات"""
        return self.data is not None
    
    def get_remaining_rows(self):
        """الحصول على عدد الصفوف المتبقية"""
        if self.data is None:
            return 0
        return self.total_rows - self.current_row
