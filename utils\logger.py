# -*- coding: utf-8 -*-
"""
نظام السجلات للبرنامج
"""

import logging
import os
from datetime import datetime
from config import LOGGING_CONFIG, LOGS_DIR


class Logger:
    """فئة إدارة السجلات"""
    
    def __init__(self, name="AutoFormFiller"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, LOGGING_CONFIG["level"]))
        
        # تجنب إضافة معالجات متعددة
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """إعداد معالجات السجلات"""
        formatter = logging.Formatter(LOGGING_CONFIG["format"])
        
        # معالج الملف
        log_file = LOGS_DIR / f"{datetime.now().strftime('%Y%m%d')}_{LOGGING_CONFIG['file_name']}"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        """تسجيل رسالة معلومات"""
        self.logger.info(message)
    
    def error(self, message):
        """تسجيل رسالة خطأ"""
        self.logger.error(message)
    
    def warning(self, message):
        """تسجيل رسالة تحذير"""
        self.logger.warning(message)
    
    def debug(self, message):
        """تسجيل رسالة تصحيح"""
        self.logger.debug(message)


# إنشاء مثيل عام للسجلات
logger = Logger()
