# -*- coding: utf-8 -*-
"""
متصفح مدمج داخل البرنامج مع إمكانية التفاعل المباشر
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select
from utils.logger import logger


class EmbeddedBrowser(ttk.Frame):
    """متصفح مدمج مع إمكانية التفاعل المباشر"""
    
    def __init__(self, parent, data_manager=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.web_driver = None
        self.current_url = ""
        self.page_elements = []
        self.selected_elements = {}
        self.automation_running = False
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة المتصفح المدمج"""
        # شريط التحكم العلوي
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # شريط الرابط
        url_frame = ttk.Frame(control_frame)
        url_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(url_frame, text="الرابط:").pack(side=tk.LEFT)
        
        self.url_var = tk.StringVar(value="https://quickly24erp.com")
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 9))
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        self.btn_go = ttk.Button(url_frame, text="🌐 انتقال", command=self.navigate_to_url)
        self.btn_go.pack(side=tk.RIGHT)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill=tk.X)
        
        self.btn_refresh = ttk.Button(buttons_frame, text="🔄 تحديث", command=self.refresh_page)
        self.btn_refresh.pack(side=tk.LEFT, padx=(0, 5))
        
        self.btn_detect = ttk.Button(buttons_frame, text="🔍 كشف العناصر", command=self.detect_elements)
        self.btn_detect.pack(side=tk.LEFT, padx=(0, 5))
        
        self.btn_auto_fill = ttk.Button(buttons_frame, text="🤖 تعبئة تلقائية", command=self.start_auto_fill)
        self.btn_auto_fill.pack(side=tk.LEFT, padx=(0, 5))
        
        self.btn_stop = ttk.Button(buttons_frame, text="⏹️ إيقاف", command=self.stop_automation, state=tk.DISABLED)
        self.btn_stop.pack(side=tk.LEFT, padx=(0, 5))
        
        # إطار المحتوى الرئيسي
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # الجانب الأيسر - عرض الصفحة
        left_frame = ttk.LabelFrame(main_frame, text="عرض الصفحة", padding=5)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # منطقة عرض محتوى الصفحة
        self.page_display = tk.Text(
            left_frame,
            wrap=tk.WORD,
            bg="#f8f9fa",
            fg="#212529",
            font=("Consolas", 9),
            state=tk.DISABLED
        )
        self.page_display.pack(fill=tk.BOTH, expand=True)
        
        # الجانب الأيمن - العناصر والتحكم
        right_frame = ttk.LabelFrame(main_frame, text="العناصر التفاعلية", padding=5)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.config(width=300)
        
        # قائمة العناصر
        self.elements_tree = ttk.Treeview(
            right_frame,
            columns=("النوع", "المعرف", "النص"),
            show="headings",
            height=15
        )
        
        for col in ("النوع", "المعرف", "النص"):
            self.elements_tree.heading(col, text=col)
            self.elements_tree.column(col, width=80)
        
        self.elements_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.elements_tree.bind('<Double-1>', self.on_element_select)
        
        # أزرار التحكم في العناصر
        elements_control = ttk.Frame(right_frame)
        elements_control.pack(fill=tk.X)
        
        self.btn_map_field = ttk.Button(
            elements_control,
            text="🔗 ربط حقل",
            command=self.map_selected_element
        )
        self.btn_map_field.pack(fill=tk.X, pady=(0, 2))
        
        self.btn_click_element = ttk.Button(
            elements_control,
            text="👆 نقر",
            command=self.click_selected_element
        )
        self.btn_click_element.pack(fill=tk.X, pady=(0, 2))
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_label = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.pack(fill=tk.X, padx=5, pady=5)
        
    def navigate_to_url(self):
        """الانتقال إلى رابط معين"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط")
            return
        
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_var.set(url)
        
        threading.Thread(target=self._navigate_thread, args=(url,), daemon=True).start()
        
    def _navigate_thread(self, url):
        """الانتقال في خيط منفصل"""
        try:
            self.status_var.set("جاري تحميل الصفحة...")
            
            if not self.web_driver:
                self._init_driver()
            
            self.web_driver.get(url)
            self.current_url = url
            
            # تحديث العرض
            self.after(0, self._update_page_display)
            self.after(0, lambda: self.status_var.set("تم تحميل الصفحة"))
            
        except Exception as e:
            logger.error(f"خطأ في الانتقال: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ: {str(e)}"))
    
    def _init_driver(self):
        """تهيئة المتصفح"""
        try:
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            options = Options()
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--window-size=1200,800")
            
            service = Service(ChromeDriverManager().install())
            self.web_driver = webdriver.Chrome(service=service, options=options)
            
            logger.info("تم تهيئة المتصفح المدمج")
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة المتصفح: {str(e)}")
            raise
    
    def _update_page_display(self):
        """تحديث عرض الصفحة"""
        try:
            if not self.web_driver:
                return
            
            # الحصول على معلومات الصفحة
            title = self.web_driver.title
            current_url = self.web_driver.current_url
            
            # استخراج محتوى الصفحة
            page_content = self._extract_page_content()
            
            # تحديث العرض
            display_content = f"🌐 {title}\n"
            display_content += f"🔗 {current_url}\n"
            display_content += "=" * 80 + "\n\n"
            display_content += page_content
            
            self.page_display.config(state=tk.NORMAL)
            self.page_display.delete(1.0, tk.END)
            self.page_display.insert(tk.END, display_content)
            self.page_display.config(state=tk.DISABLED)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث العرض: {str(e)}")
    
    def _extract_page_content(self):
        """استخراج محتوى الصفحة"""
        try:
            # الحصول على النصوص المرئية
            visible_texts = self.web_driver.find_elements(By.XPATH, "//*[text()]")
            
            content_lines = []
            for element in visible_texts[:50]:  # أول 50 عنصر
                try:
                    if element.is_displayed() and element.text.strip():
                        text = element.text.strip()
                        if len(text) > 2 and len(text) < 100:
                            content_lines.append(f"• {text}")
                except:
                    continue
            
            return '\n'.join(content_lines) if content_lines else "لا يوجد محتوى نصي واضح"
            
        except Exception as e:
            logger.error(f"خطأ في استخراج المحتوى: {str(e)}")
            return "خطأ في استخراج المحتوى"
    
    def detect_elements(self):
        """كشف العناصر التفاعلية"""
        if not self.web_driver:
            messagebox.showwarning("تحذير", "يرجى الانتقال لصفحة أولاً")
            return
        
        threading.Thread(target=self._detect_elements_thread, daemon=True).start()
    
    def _detect_elements_thread(self):
        """كشف العناصر في خيط منفصل"""
        try:
            self.after(0, lambda: self.status_var.set("جاري كشف العناصر..."))
            
            # مسح القائمة السابقة
            self.after(0, lambda: self.elements_tree.delete(*self.elements_tree.get_children()))
            
            self.page_elements = []
            
            # البحث عن أنواع مختلفة من العناصر
            element_selectors = [
                ("input", "حقل إدخال"),
                ("button", "زر"),
                ("select", "قائمة منسدلة"),
                ("textarea", "منطقة نص"),
                ("a", "رابط"),
                ("[onclick]", "عنصر قابل للنقر"),
                ("[type='submit']", "زر إرسال"),
                ("[type='button']", "زر"),
                ("[role='button']", "زر"),
                (".btn", "زر"),
                (".button", "زر")
            ]
            
            for selector, element_type in element_selectors:
                try:
                    elements = self.web_driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                element_info = {
                                    "element": element,
                                    "type": element_type,
                                    "tag": element.tag_name,
                                    "id": element.get_attribute("id") or "",
                                    "name": element.get_attribute("name") or "",
                                    "class": element.get_attribute("class") or "",
                                    "text": element.text[:30] if element.text else "",
                                    "placeholder": element.get_attribute("placeholder") or "",
                                    "value": element.get_attribute("value") or "",
                                    "xpath": self._get_element_xpath(element)
                                }
                                
                                self.page_elements.append(element_info)
                                
                        except Exception as e:
                            logger.debug(f"خطأ في معالجة عنصر: {str(e)}")
                            continue
                            
                except Exception as e:
                    logger.debug(f"خطأ في البحث عن {selector}: {str(e)}")
                    continue
            
            # تحديث القائمة
            self.after(0, self._update_elements_list)
            self.after(0, lambda: self.status_var.set(f"تم العثور على {len(self.page_elements)} عنصر"))
            
        except Exception as e:
            logger.error(f"خطأ في كشف العناصر: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ في كشف العناصر"))
    
    def _get_element_xpath(self, element):
        """الحصول على XPath للعنصر"""
        try:
            return self.web_driver.execute_script(
                "function getXPath(element) {"
                "if (element.id !== '') return '//*[@id=\"' + element.id + '\"]';"
                "if (element === document.body) return '/html/body';"
                "var ix = 0;"
                "var siblings = element.parentNode.childNodes;"
                "for (var i = 0; i < siblings.length; i++) {"
                "var sibling = siblings[i];"
                "if (sibling === element) return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';"
                "if (sibling.nodeType === 1 && sibling.tagName === element.tagName) ix++;"
                "}"
                "}"
                "return getXPath(arguments[0]);", element
            )
        except:
            return None
    
    def _update_elements_list(self):
        """تحديث قائمة العناصر"""
        try:
            for element_info in self.page_elements:
                display_text = element_info["text"] or element_info["placeholder"] or element_info["id"] or "عنصر"
                
                self.elements_tree.insert("", tk.END, values=(
                    element_info["type"],
                    element_info["id"][:15],
                    display_text[:20]
                ))
                
        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة العناصر: {str(e)}")
    
    def refresh_page(self):
        """تحديث الصفحة"""
        if self.web_driver and self.current_url:
            threading.Thread(target=self._navigate_thread, args=(self.current_url,), daemon=True).start()
    
    def on_element_select(self, event):
        """عند تحديد عنصر من القائمة"""
        try:
            selection = self.elements_tree.selection()
            if selection:
                item = selection[0]
                index = self.elements_tree.index(item)
                
                if 0 <= index < len(self.page_elements):
                    element_info = self.page_elements[index]
                    self.status_var.set(f"تم تحديد: {element_info['type']} - {element_info['id']}")
                    
        except Exception as e:
            logger.error(f"خطأ في تحديد العنصر: {str(e)}")
    
    def map_selected_element(self):
        """ربط العنصر المحدد بحقل بيانات"""
        # سيتم تطوير هذه الوظيفة
        messagebox.showinfo("قريباً", "ميزة ربط الحقول قيد التطوير")
    
    def click_selected_element(self):
        """النقر على العنصر المحدد"""
        try:
            selection = self.elements_tree.selection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى تحديد عنصر أولاً")
                return
            
            item = selection[0]
            index = self.elements_tree.index(item)
            
            if 0 <= index < len(self.page_elements):
                element_info = self.page_elements[index]
                element = element_info["element"]
                
                # النقر على العنصر
                threading.Thread(target=self._click_element_thread, args=(element,), daemon=True).start()
                
        except Exception as e:
            logger.error(f"خطأ في النقر على العنصر: {str(e)}")
            messagebox.showerror("خطأ", f"خطأ في النقر على العنصر: {str(e)}")
    
    def _click_element_thread(self, element):
        """النقر على العنصر في خيط منفصل"""
        try:
            self.after(0, lambda: self.status_var.set("جاري النقر على العنصر..."))
            
            # التمرير للعنصر
            self.web_driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # النقر
            element.click()
            
            self.after(0, lambda: self.status_var.set("تم النقر على العنصر"))
            
            # تحديث العرض
            time.sleep(1)
            self.after(0, self._update_page_display)
            
        except Exception as e:
            logger.error(f"خطأ في النقر: {str(e)}")
            self.after(0, lambda: self.status_var.set(f"خطأ في النقر: {str(e)}"))
    
    def start_auto_fill(self):
        """بدء التعبئة التلقائية"""
        if not self.data_manager or not self.data_manager.is_data_loaded():
            messagebox.showwarning("تحذير", "يرجى تحميل البيانات أولاً")
            return
        
        if not self.page_elements:
            messagebox.showwarning("تحذير", "يرجى كشف العناصر أولاً")
            return
        
        self.automation_running = True
        self.btn_auto_fill.config(state=tk.DISABLED)
        self.btn_stop.config(state=tk.NORMAL)
        
        threading.Thread(target=self._auto_fill_thread, daemon=True).start()
    
    def _auto_fill_thread(self):
        """خيط التعبئة التلقائية"""
        try:
            self.data_manager.reset_position()
            
            while self.automation_running and self.data_manager.get_remaining_rows() > 0:
                # الحصول على بيانات الصف الحالي
                row_data = self.data_manager.get_current_row_data()
                if not row_data:
                    break
                
                self.after(0, lambda: self.status_var.set(f"تعبئة الصف {self.data_manager.current_row + 1}"))
                
                # تعبئة الحقول
                success = self._fill_form_fields(row_data)
                
                if success:
                    # الانتقال للصف التالي
                    self.data_manager.get_next_row_data()
                    time.sleep(2)  # تأخير بين الصفوف
                else:
                    logger.warning("فشل في تعبئة الصف")
                    break
            
            self.after(0, self._auto_fill_finished)
            
        except Exception as e:
            logger.error(f"خطأ في التعبئة التلقائية: {str(e)}")
            self.after(0, self._auto_fill_finished)
    
    def _fill_form_fields(self, row_data):
        """تعبئة حقول النموذج"""
        try:
            # البحث عن حقول الإدخال وتعبئتها
            for element_info in self.page_elements:
                if element_info["type"] in ["حقل إدخال", "منطقة نص"]:
                    element = element_info["element"]
                    
                    # محاولة تحديد نوع الحقل من اسمه أو معرفه
                    field_name = self._identify_field_type(element_info)
                    
                    if field_name and field_name in row_data:
                        try:
                            # تنظيف الحقل وإدخال القيمة
                            element.clear()
                            element.send_keys(str(row_data[field_name]))
                            time.sleep(0.5)
                            
                        except Exception as e:
                            logger.debug(f"خطأ في تعبئة الحقل {field_name}: {str(e)}")
                            continue
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تعبئة النموذج: {str(e)}")
            return False
    
    def _identify_field_type(self, element_info):
        """تحديد نوع الحقل من معلوماته"""
        # البحث في الخصائص المختلفة
        search_terms = [
            element_info.get("name", "").lower(),
            element_info.get("id", "").lower(),
            element_info.get("placeholder", "").lower(),
            element_info.get("class", "").lower()
        ]
        
        # مطابقة مع أسماء الأعمدة
        for column_name in self.data_manager.get_column_names():
            column_lower = column_name.lower()
            
            for term in search_terms:
                if term and (column_lower in term or term in column_lower):
                    return column_name
        
        return None
    
    def _auto_fill_finished(self):
        """عند انتهاء التعبئة التلقائية"""
        self.automation_running = False
        self.btn_auto_fill.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.status_var.set("تم إكمال التعبئة التلقائية")
        messagebox.showinfo("مكتمل", "تم إكمال التعبئة التلقائية")
    
    def stop_automation(self):
        """إيقاف التعبئة التلقائية"""
        self.automation_running = False
        self.btn_auto_fill.config(state=tk.NORMAL)
        self.btn_stop.config(state=tk.DISABLED)
        self.status_var.set("تم إيقاف التعبئة التلقائية")
    
    def close_browser(self):
        """إغلاق المتصفح"""
        try:
            if self.web_driver:
                self.web_driver.quit()
                self.web_driver = None
                logger.info("تم إغلاق المتصفح المدمج")
        except Exception as e:
            logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")
