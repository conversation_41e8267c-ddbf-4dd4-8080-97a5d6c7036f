# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للنظام الذكي
واجهة مستخدم متقدمة للتعرف الذكي والتنفيذ التلقائي
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from pathlib import Path
import threading
import time
from datetime import datetime
import json

from config import UI_CONFIG
from data_management.data_manager import DataManager
from automation.web_driver import WebDriverManager
from ai_automation.smart_element_detector import SmartElementDetector
from ai_automation.smart_action_tracker import SmartActionTracker
from ai_automation.smart_execution_engine import SmartExecutionEngine
from gui.real_webview import RealWebView
from utils.logger import logger

class SmartMainWindow:
    """النافذة الرئيسية للنظام الذكي"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()

        # المكونات الأساسية
        self.data_manager = DataManager()
        self.web_driver_manager = WebDriverManager()
        self.web_driver = None

        # المكونات الذكية
        self.element_detector = None
        self.action_tracker = None
        self.execution_engine = None

        # البيانات
        self.current_data = None
        self.learned_pattern = None

        # حالة النظام
        self.is_tracking = False
        self.is_executing = False

        # إنشاء الواجهة
        self.create_widgets()
        self.setup_layout()

        logger.info("تم تهيئة النافذة الذكية")

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("نظام تعبئة البيانات الذكي - AI Powered")
        self.root.geometry("1600x900")
        self.root.configure(bg='#f0f0f0')

        # إعداد الخط
        self.default_font = ('Arial', 10)
        self.header_font = ('Arial', 12, 'bold')

        # إعداد الألوان
        self.colors = {
            'primary': '#2196F3',
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'background': '#f0f0f0',
            'card': '#ffffff'
        }

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""

        # الشريط العلوي
        self.create_header()

        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.root)

        # الجانب الأيسر - المتصفح والتحكم
        self.left_frame = ttk.Frame(self.main_frame)
        self.create_browser_section()
        self.create_smart_controls()

        # الجانب الأيمن - البيانات والنتائج
        self.right_frame = ttk.Frame(self.main_frame)
        self.create_data_section()
        self.create_pattern_section()
        self.create_results_section()

        # شريط الحالة
        self.create_status_bar()

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        header_frame.pack_propagate(False)

        # العنوان
        title_label = tk.Label(
            header_frame,
            text="🤖 نظام تعبئة البيانات الذكي",
            font=('Arial', 16, 'bold'),
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # معلومات النظام
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        self.ai_status_label = tk.Label(
            info_frame,
            text="🧠 الذكاء الاصطناعي: جاهز",
            font=('Arial', 10),
            bg=self.colors['primary'],
            fg='white'
        )
        self.ai_status_label.pack()

        self.pattern_status_label = tk.Label(
            info_frame,
            text="📋 النمط: غير متعلم",
            font=('Arial', 10),
            bg=self.colors['primary'],
            fg='white'
        )
        self.pattern_status_label.pack()

    def create_browser_section(self):
        """إنشاء قسم المتصفح"""
        browser_frame = ttk.LabelFrame(self.left_frame, text="🌐 المتصفح الذكي", padding=10)
        browser_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط الرابط
        url_frame = ttk.Frame(browser_frame)
        url_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(url_frame, text="الرابط:").pack(side=tk.LEFT, padx=(0, 5))

        self.url_entry = ttk.Entry(url_frame, font=self.default_font)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.url_entry.insert(0, "https://")

        self.btn_open_browser = ttk.Button(
            url_frame,
            text="فتح",
            command=self.open_browser
        )
        self.btn_open_browser.pack(side=tk.RIGHT)

        # المتصفح المدمج
        self.webview_frame = ttk.Frame(browser_frame)
        self.webview_frame.pack(fill=tk.BOTH, expand=True)

        # سيتم إنشاء المتصفح عند الحاجة
        self.webview = None

    def create_smart_controls(self):
        """إنشاء أدوات التحكم الذكية"""
        controls_frame = ttk.LabelFrame(self.left_frame, text="🎯 التحكم الذكي", padding=10)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # أزرار التحكم الرئيسية
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.btn_analyze_page = ttk.Button(
            buttons_frame,
            text="🔍 تحليل الصفحة",
            command=self.analyze_page,
            state=tk.DISABLED
        )
        self.btn_analyze_page.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_start_learning = ttk.Button(
            buttons_frame,
            text="🧠 بدء التعلم",
            command=self.start_learning,
            state=tk.DISABLED
        )
        self.btn_start_learning.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_learning = ttk.Button(
            buttons_frame,
            text="⏹️ إيقاف التعلم",
            command=self.stop_learning,
            state=tk.DISABLED
        )
        self.btn_stop_learning.pack(side=tk.LEFT, padx=(0, 5))

        # أزرار التنفيذ
        execution_frame = ttk.Frame(controls_frame)
        execution_frame.pack(fill=tk.X)

        self.btn_execute_pattern = ttk.Button(
            execution_frame,
            text="🚀 تنفيذ تلقائي",
            command=self.execute_pattern,
            state=tk.DISABLED
        )
        self.btn_execute_pattern.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_execution = ttk.Button(
            execution_frame,
            text="⏸️ إيقاف التنفيذ",
            command=self.stop_execution,
            state=tk.DISABLED
        )
        self.btn_stop_execution.pack(side=tk.LEFT, padx=(0, 5))

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            controls_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))

    def create_data_section(self):
        """إنشاء قسم البيانات"""
        data_frame = ttk.LabelFrame(self.right_frame, text="📊 البيانات", padding=10)
        data_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # أزرار البيانات
        data_buttons_frame = ttk.Frame(data_frame)
        data_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.btn_load_data = ttk.Button(
            data_buttons_frame,
            text="📁 تحميل البيانات",
            command=self.load_data
        )
        self.btn_load_data.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_clear_data = ttk.Button(
            data_buttons_frame,
            text="🗑️ مسح البيانات",
            command=self.clear_data
        )
        self.btn_clear_data.pack(side=tk.LEFT)

        # جدول البيانات
        self.create_data_table(data_frame)

    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview
        self.data_tree = ttk.Treeview(table_frame, show='headings')

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set)

        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(xscrollcommand=h_scrollbar.set)

        # تخطيط الجدول
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

    def create_pattern_section(self):
        """إنشاء قسم النمط المتعلم"""
        pattern_frame = ttk.LabelFrame(self.right_frame, text="🧩 النمط المتعلم", padding=10)
        pattern_frame.pack(fill=tk.X, padx=5, pady=5)

        # معلومات النمط
        self.pattern_info_text = tk.Text(
            pattern_frame,
            height=8,
            font=('Courier', 9),
            bg='#f8f8f8',
            state=tk.DISABLED
        )
        self.pattern_info_text.pack(fill=tk.X)

        # أزرار النمط
        pattern_buttons_frame = ttk.Frame(pattern_frame)
        pattern_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        self.btn_save_pattern = ttk.Button(
            pattern_buttons_frame,
            text="💾 حفظ النمط",
            command=self.save_pattern,
            state=tk.DISABLED
        )
        self.btn_save_pattern.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_load_pattern = ttk.Button(
            pattern_buttons_frame,
            text="📂 تحميل نمط",
            command=self.load_pattern
        )
        self.btn_load_pattern.pack(side=tk.LEFT)

    def create_results_section(self):
        """إنشاء قسم النتائج"""
        results_frame = ttk.LabelFrame(self.right_frame, text="📈 النتائج", padding=10)
        results_frame.pack(fill=tk.X, padx=5, pady=5)

        # إحصائيات سريعة
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.stats_labels = {}
        stats_info = [
            ('total', 'المجموع: 0'),
            ('success', 'نجح: 0'),
            ('failed', 'فشل: 0'),
            ('rate', 'المعدل: 0%')
        ]

        for i, (key, text) in enumerate(stats_info):
            label = ttk.Label(stats_frame, text=text, font=self.default_font)
            label.grid(row=0, column=i, padx=10, sticky='w')
            self.stats_labels[key] = label

        # سجل النتائج
        self.results_text = tk.Text(
            results_frame,
            height=6,
            font=('Courier', 9),
            bg='#f8f8f8',
            state=tk.DISABLED
        )
        self.results_text.pack(fill=tk.X)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            bg='#e0e0e0',
            font=('Arial', 9),
            anchor='w'
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)

        # ساعة
        self.time_label = tk.Label(
            status_frame,
            text="",
            bg='#e0e0e0',
            font=('Arial', 9)
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=2)

        self.update_time()

    def setup_layout(self):
        """ترتيب التخطيط"""
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تقسيم الشاشة: 60% يسار، 40% يمين
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # تحديد عرض الجانب الأيمن
        self.right_frame.configure(width=500)
        self.right_frame.pack_propagate(False)