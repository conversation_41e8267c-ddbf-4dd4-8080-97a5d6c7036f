#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 نظام تعبئة البيانات الذكي - النظام المستقل
===============================================

نظام ذكي متكامل لأتمتة تعبئة النماذج الإلكترونية باستخدام:
- الذكاء الاصطناعي للتعرف على العناصر
- التعلم الآلي لفهم الأنماط
- الرؤية الحاسوبية لتحليل الواجهات
- التنفيذ التكيفي للمهام

المؤلف: نظام الذكاء الاصطناعي
التاريخ: 2025
الإصدار: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import json
import time
import threading
from datetime import datetime
from pathlib import Path
import logging

# إضافة مسار المشروع للـ Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# الاستيراد من المكونات الذكية
try:
    from ai_automation.smart_element_detector import SmartElementDetector
    from ai_automation.smart_action_tracker import SmartActionTracker
    from ai_automation.smart_execution_engine import SmartExecutionEngine
    from automation.web_driver import WebDriverManager
    from data_management.data_manager import DataManager
    from utils.logger import logger
except ImportError as e:
    print(f"خطأ في استيراد المكونات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)

class SmartFormFillerSystem:
    """النظام الذكي المتكامل لتعبئة النماذج"""

    def __init__(self):
        """تهيئة النظام الذكي"""
        self.version = "1.0.0"
        self.title = "🤖 نظام تعبئة البيانات الذكي"

        # إعداد النافذة الرئيسية
        self.root = tk.Tk()
        self.setup_main_window()

        # المكونات الأساسية
        self.web_driver_manager = WebDriverManager()
        self.data_manager = DataManager()
        self.web_driver = None

        # المكونات الذكية
        self.element_detector = None
        self.action_tracker = None
        self.execution_engine = None

        # حالة النظام
        self.current_data = None
        self.learned_pattern = None
        self.is_learning = False
        self.is_executing = False

        # إنشاء الواجهة
        self.create_interface()

        # إعداد السجلات
        self.setup_logging()

        logger.info(f"تم تهيئة {self.title} - الإصدار {self.version}")

    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(self.title)
        self.root.geometry("1400x800")
        self.root.configure(bg='#f5f5f5')

        # منع تغيير الحجم
        self.root.resizable(True, True)

        # إعداد الخطوط والألوان
        self.fonts = {
            'title': ('Arial', 14, 'bold'),
            'header': ('Arial', 12, 'bold'),
            'normal': ('Arial', 10),
            'small': ('Arial', 9)
        }

        self.colors = {
            'primary': '#2196F3',
            'success': '#4CAF50',
            'warning': '#FF9800',
            'error': '#F44336',
            'info': '#00BCD4',
            'background': '#f5f5f5',
            'card': '#ffffff',
            'border': '#e0e0e0'
        }

        # إعداد الأيقونات النصية
        self.icons = {
            'ai': '🤖',
            'brain': '🧠',
            'eye': '👁️',
            'target': '🎯',
            'rocket': '🚀',
            'data': '📊',
            'file': '📁',
            'save': '💾',
            'load': '📂',
            'play': '▶️',
            'stop': '⏹️',
            'pause': '⏸️',
            'settings': '⚙️',
            'info': 'ℹ️',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        }

    def create_interface(self):
        """إنشاء واجهة المستخدم الذكية"""

        # الشريط العلوي
        self.create_header()

        # الإطار الرئيسي
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # تقسيم الشاشة
        self.create_left_panel(main_container)
        self.create_right_panel(main_container)

        # شريط الحالة
        self.create_status_bar()

        # ربط الأحداث
        self.bind_events()

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=70)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # العنوان الرئيسي
        title_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        title_frame.pack(side=tk.LEFT, padx=20, pady=10)

        title_label = tk.Label(
            title_frame,
            text=f"{self.icons['ai']} نظام تعبئة البيانات الذكي",
            font=self.fonts['title'],
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack()

        subtitle_label = tk.Label(
            title_frame,
            text="تعلم ذكي • تنفيذ تلقائي • دقة عالية",
            font=self.fonts['small'],
            bg=self.colors['primary'],
            fg='#E3F2FD'
        )
        subtitle_label.pack()

        # معلومات النظام
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        self.system_status = tk.Label(
            info_frame,
            text=f"{self.icons['brain']} الذكاء الاصطناعي: جاهز",
            font=self.fonts['small'],
            bg=self.colors['primary'],
            fg='white'
        )
        self.system_status.pack()

        self.pattern_status = tk.Label(
            info_frame,
            text=f"{self.icons['target']} النمط: غير متعلم",
            font=self.fonts['small'],
            bg=self.colors['primary'],
            fg='white'
        )
        self.pattern_status.pack()

        version_label = tk.Label(
            info_frame,
            text=f"الإصدار {self.version}",
            font=self.fonts['small'],
            bg=self.colors['primary'],
            fg='#E3F2FD'
        )
        version_label.pack()

    def create_left_panel(self, parent):
        """إنشاء اللوحة اليسرى - المتصفح والتحكم"""
        self.left_frame = ttk.Frame(parent)
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # قسم المتصفح
        self.create_browser_section()

        # قسم التحكم الذكي
        self.create_smart_controls()

    def create_browser_section(self):
        """إنشاء قسم المتصفح"""
        browser_frame = ttk.LabelFrame(
            self.left_frame,
            text=f"{self.icons['eye']} المتصفح الذكي",
            padding=10
        )
        browser_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # شريط الرابط
        url_frame = ttk.Frame(browser_frame)
        url_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(url_frame, text="رابط الموقع:", font=self.fonts['normal']).pack(side=tk.LEFT, padx=(0, 5))

        self.url_var = tk.StringVar(value="https://")
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=self.fonts['normal'])
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.btn_open_browser = ttk.Button(
            url_frame,
            text=f"{self.icons['play']} فتح",
            command=self.open_browser
        )
        self.btn_open_browser.pack(side=tk.RIGHT)

        # منطقة المتصفح
        self.browser_container = ttk.Frame(browser_frame)
        self.browser_container.pack(fill=tk.BOTH, expand=True)

        # رسالة ترحيب
        welcome_frame = tk.Frame(self.browser_container, bg='white', relief=tk.RAISED, bd=1)
        welcome_frame.pack(fill=tk.BOTH, expand=True)

        welcome_label = tk.Label(
            welcome_frame,
            text=f"{self.icons['info']} أدخل رابط الموقع واضغط 'فتح' لبدء التحليل الذكي",
            font=self.fonts['normal'],
            bg='white',
            fg='#666666'
        )
        welcome_label.pack(expand=True)

    def create_smart_controls(self):
        """إنشاء أدوات التحكم الذكية"""
        controls_frame = ttk.LabelFrame(
            self.left_frame,
            text=f"{self.icons['brain']} التحكم الذكي",
            padding=10
        )
        controls_frame.pack(fill=tk.X, pady=5)

        # الصف الأول - التحليل والتعلم
        row1_frame = ttk.Frame(controls_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))

        self.btn_analyze = ttk.Button(
            row1_frame,
            text=f"{self.icons['eye']} تحليل الصفحة",
            command=self.analyze_page,
            state=tk.DISABLED
        )
        self.btn_analyze.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_start_learning = ttk.Button(
            row1_frame,
            text=f"{self.icons['brain']} بدء التعلم",
            command=self.start_learning,
            state=tk.DISABLED
        )
        self.btn_start_learning.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_learning = ttk.Button(
            row1_frame,
            text=f"{self.icons['stop']} إيقاف التعلم",
            command=self.stop_learning,
            state=tk.DISABLED
        )
        self.btn_stop_learning.pack(side=tk.LEFT)

        # الصف الثاني - التنفيذ
        row2_frame = ttk.Frame(controls_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 10))

        self.btn_execute = ttk.Button(
            row2_frame,
            text=f"{self.icons['rocket']} تنفيذ ذكي",
            command=self.execute_smart,
            state=tk.DISABLED
        )
        self.btn_execute.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_stop_execution = ttk.Button(
            row2_frame,
            text=f"{self.icons['pause']} إيقاف التنفيذ",
            command=self.stop_execution,
            state=tk.DISABLED
        )
        self.btn_stop_execution.pack(side=tk.LEFT)

        # شريط التقدم
        progress_frame = ttk.Frame(controls_frame)
        progress_frame.pack(fill=tk.X)

        ttk.Label(progress_frame, text="التقدم:", font=self.fonts['small']).pack(side=tk.LEFT, padx=(0, 5))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=200
        )
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.progress_label = ttk.Label(progress_frame, text="0%", font=self.fonts['small'])
        self.progress_label.pack(side=tk.RIGHT)

    def create_right_panel(self, parent):
        """إنشاء اللوحة اليمنى - البيانات والنتائج"""
        self.right_frame = ttk.Frame(parent)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        self.right_frame.configure(width=400)
        self.right_frame.pack_propagate(False)

        # قسم البيانات
        self.create_data_section()

        # قسم النمط المتعلم
        self.create_pattern_section()

        # قسم النتائج
        self.create_results_section()

    def create_data_section(self):
        """إنشاء قسم البيانات"""
        data_frame = ttk.LabelFrame(
            self.right_frame,
            text=f"{self.icons['data']} البيانات",
            padding=10
        )
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # أزرار البيانات
        data_buttons = ttk.Frame(data_frame)
        data_buttons.pack(fill=tk.X, pady=(0, 10))

        self.btn_load_data = ttk.Button(
            data_buttons,
            text=f"{self.icons['file']} تحميل",
            command=self.load_data
        )
        self.btn_load_data.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_clear_data = ttk.Button(
            data_buttons,
            text="مسح",
            command=self.clear_data
        )
        self.btn_clear_data.pack(side=tk.LEFT)

        # معلومات البيانات
        self.data_info = ttk.Label(
            data_frame,
            text="لا توجد بيانات محملة",
            font=self.fonts['small'],
            foreground='#666666'
        )
        self.data_info.pack(fill=tk.X, pady=(0, 5))

        # جدول البيانات (مبسط)
        self.data_display = tk.Text(
            data_frame,
            height=8,
            font=('Courier', 9),
            bg='#f8f8f8',
            state=tk.DISABLED,
            wrap=tk.WORD
        )
        self.data_display.pack(fill=tk.BOTH, expand=True)

    def create_pattern_section(self):
        """إنشاء قسم النمط المتعلم"""
        pattern_frame = ttk.LabelFrame(
            self.right_frame,
            text=f"{self.icons['target']} النمط المتعلم",
            padding=10
        )
        pattern_frame.pack(fill=tk.X, pady=5)

        # أزرار النمط
        pattern_buttons = ttk.Frame(pattern_frame)
        pattern_buttons.pack(fill=tk.X, pady=(0, 10))

        self.btn_save_pattern = ttk.Button(
            pattern_buttons,
            text=f"{self.icons['save']} حفظ",
            command=self.save_pattern,
            state=tk.DISABLED
        )
        self.btn_save_pattern.pack(side=tk.LEFT, padx=(0, 5))

        self.btn_load_pattern = ttk.Button(
            pattern_buttons,
            text=f"{self.icons['load']} تحميل",
            command=self.load_pattern
        )
        self.btn_load_pattern.pack(side=tk.LEFT)

        # عرض النمط
        self.pattern_display = tk.Text(
            pattern_frame,
            height=6,
            font=('Courier', 9),
            bg='#f8f8f8',
            state=tk.DISABLED,
            wrap=tk.WORD
        )
        self.pattern_display.pack(fill=tk.X)

    def create_results_section(self):
        """إنشاء قسم النتائج"""
        results_frame = ttk.LabelFrame(
            self.right_frame,
            text=f"{self.icons['info']} النتائج والإحصائيات",
            padding=10
        )
        results_frame.pack(fill=tk.X, pady=(5, 0))

        # الإحصائيات
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.stats_labels = {}
        stats_data = [
            ('processed', 'معالج: 0'),
            ('success', 'نجح: 0'),
            ('failed', 'فشل: 0'),
            ('rate', 'المعدل: 0%')
        ]

        for i, (key, text) in enumerate(stats_data):
            row = i // 2
            col = i % 2
            label = ttk.Label(stats_frame, text=text, font=self.fonts['small'])
            label.grid(row=row, column=col, sticky='w', padx=5, pady=2)
            self.stats_labels[key] = label

        # سجل النتائج
        self.results_log = tk.Text(
            results_frame,
            height=5,
            font=('Courier', 8),
            bg='#f8f8f8',
            state=tk.DISABLED,
            wrap=tk.WORD
        )
        self.results_log.pack(fill=tk.X)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#e0e0e0', height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        self.status_text = tk.StringVar(value="جاهز للعمل")
        self.status_label = tk.Label(
            status_frame,
            textvariable=self.status_text,
            bg='#e0e0e0',
            font=self.fonts['small'],
            anchor='w'
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)

        # الوقت
        self.time_label = tk.Label(
            status_frame,
            text="",
            bg='#e0e0e0',
            font=self.fonts['small']
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=2)

        self.update_time()

    def bind_events(self):
        """ربط الأحداث"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.url_entry.bind('<Return>', lambda e: self.open_browser())

    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        log_file = log_dir / f"smart_system_{datetime.now().strftime('%Y%m%d')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    def update_status(self, message: str, level: str = "info"):
        """تحديث رسالة الحالة"""
        self.status_text.set(message)

        # تلوين الرسالة حسب المستوى
        colors = {
            'info': '#333333',
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error']
        }

        self.status_label.config(fg=colors.get(level, '#333333'))

        # إضافة إلى السجل
        if level == 'error':
            logger.error(message)
        elif level == 'warning':
            logger.warning(message)
        else:
            logger.info(message)

    # ==================== الوظائف الأساسية ====================

    def open_browser(self):
        """فتح المتصفح وتحميل الموقع"""
        try:
            url = self.url_var.get().strip()
            if not url or url == "https://":
                messagebox.showwarning("تحذير", "يرجى إدخال رابط صحيح")
                return

            self.update_status("جاري فتح المتصفح...")
            self.btn_open_browser.config(state=tk.DISABLED)

            # فتح المتصفح في خيط منفصل
            threading.Thread(target=self._open_browser_thread, args=(url,), daemon=True).start()

        except Exception as e:
            self.update_status(f"خطأ في فتح المتصفح: {str(e)}", "error")
            self.btn_open_browser.config(state=tk.NORMAL)

    def _open_browser_thread(self, url: str):
        """فتح المتصفح في خيط منفصل"""
        try:
            # إنشاء مدير المتصفح
            self.web_driver = self.web_driver_manager.create_driver()

            # تحميل الصفحة
            self.web_driver.get(url)

            # تهيئة المكونات الذكية
            self.element_detector = SmartElementDetector(self.web_driver)
            self.action_tracker = SmartActionTracker(self.web_driver)
            self.execution_engine = SmartExecutionEngine(self.web_driver)

            # تحديث الواجهة
            self.root.after(0, self._on_browser_opened)

        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"خطأ في فتح المتصفح: {str(e)}", "error"))
            self.root.after(0, lambda: self.btn_open_browser.config(state=tk.NORMAL))

    def _on_browser_opened(self):
        """استدعاء عند فتح المتصفح بنجاح"""
        self.update_status("تم فتح المتصفح بنجاح", "success")
        self.system_status.config(text=f"{self.icons['success']} الذكاء الاصطناعي: متصل")

        # تفعيل الأزرار
        self.btn_analyze.config(state=tk.NORMAL)
        self.btn_start_learning.config(state=tk.NORMAL)

        # إخفاء رسالة الترحيب وإظهار معلومات الصفحة
        for widget in self.browser_container.winfo_children():
            widget.destroy()

        info_frame = tk.Frame(self.browser_container, bg='white', relief=tk.RAISED, bd=1)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # معلومات الصفحة
        page_title = self.web_driver.title
        page_url = self.web_driver.current_url

        title_label = tk.Label(
            info_frame,
            text=f"العنوان: {page_title}",
            font=self.fonts['normal'],
            bg='white',
            anchor='w'
        )
        title_label.pack(fill=tk.X, padx=10, pady=5)

        url_label = tk.Label(
            info_frame,
            text=f"الرابط: {page_url}",
            font=self.fonts['small'],
            bg='white',
            fg='#666666',
            anchor='w'
        )
        url_label.pack(fill=tk.X, padx=10, pady=(0, 10))

        # زر التحليل السريع
        quick_analyze_btn = ttk.Button(
            info_frame,
            text=f"{self.icons['eye']} تحليل سريع",
            command=self.quick_analyze
        )
        quick_analyze_btn.pack(pady=10)

    def analyze_page(self):
        """تحليل الصفحة بالذكاء الاصطناعي"""
        try:
            if not self.element_detector:
                self.update_status("المتصفح غير متصل", "error")
                return

            self.update_status("جاري تحليل الصفحة...")
            self.btn_analyze.config(state=tk.DISABLED)

            # تحليل في خيط منفصل
            threading.Thread(target=self._analyze_page_thread, daemon=True).start()

        except Exception as e:
            self.update_status(f"خطأ في التحليل: {str(e)}", "error")
            self.btn_analyze.config(state=tk.NORMAL)

    def _analyze_page_thread(self):
        """تحليل الصفحة في خيط منفصل"""
        try:
            # تحليل هيكل الصفحة
            analysis_result = self.element_detector.analyze_page_structure()

            # تحديث الواجهة
            self.root.after(0, lambda: self._on_analysis_complete(analysis_result))

        except Exception as e:
            self.root.after(0, lambda: self.update_status(f"خطأ في التحليل: {str(e)}", "error"))
            self.root.after(0, lambda: self.btn_analyze.config(state=tk.NORMAL))

    def _on_analysis_complete(self, analysis_result: dict):
        """استدعاء عند اكتمال التحليل"""
        if analysis_result:
            elements_count = len(analysis_result.get('elements', []))
            forms_count = analysis_result.get('forms_count', 0)
            inputs_count = analysis_result.get('inputs_count', 0)

            self.update_status(f"تم تحليل {elements_count} عنصر ({forms_count} نموذج، {inputs_count} حقل)", "success")

            # عرض نتائج التحليل
            self._display_analysis_results(analysis_result)
        else:
            self.update_status("فشل في تحليل الصفحة", "error")

        self.btn_analyze.config(state=tk.NORMAL)

    def quick_analyze(self):
        """تحليل سريع للصفحة"""
        try:
            if not self.element_detector:
                return

            # تحليل سريع
            interactive_elements = self.element_detector.get_interactive_elements()

            if interactive_elements:
                count = len(interactive_elements)
                self.update_status(f"تم العثور على {count} عنصر تفاعلي", "success")
            else:
                self.update_status("لم يتم العثور على عناصر تفاعلية", "warning")

        except Exception as e:
            self.update_status(f"خطأ في التحليل السريع: {str(e)}", "error")

    def start_learning(self):
        """بدء التعلم الذكي"""
        try:
            if not self.action_tracker:
                self.update_status("المتصفح غير متصل", "error")
                return

            if self.is_learning:
                self.update_status("التعلم قيد التشغيل بالفعل", "warning")
                return

            # بدء التتبع
            session_name = f"smart_learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            success = self.action_tracker.start_tracking(session_name)

            if success:
                self.is_learning = True
                self.update_status("بدء التعلم الذكي - قم بتعبئة النموذج", "success")
                self.pattern_status.config(text=f"{self.icons['brain']} النمط: جاري التعلم...")

                # تحديث الأزرار
                self.btn_start_learning.config(state=tk.DISABLED)
                self.btn_stop_learning.config(state=tk.NORMAL)

                # إعداد callbacks
                self.action_tracker.on_action_detected = self._on_action_detected
                self.action_tracker.on_pattern_learned = self._on_pattern_learned
            else:
                self.update_status("فشل في بدء التعلم", "error")

        except Exception as e:
            self.update_status(f"خطأ في بدء التعلم: {str(e)}", "error")

    def stop_learning(self):
        """إيقاف التعلم وتحليل النمط"""
        try:
            if not self.is_learning:
                self.update_status("التعلم غير نشط", "warning")
                return

            self.update_status("جاري إيقاف التعلم وتحليل النمط...")

            # إيقاف التتبع وتحليل النمط
            learned_pattern = self.action_tracker.stop_tracking()

            if learned_pattern:
                self.learned_pattern = learned_pattern
                self._display_learned_pattern(learned_pattern)

                confidence = learned_pattern.get('confidence_score', 0)
                actions_count = learned_pattern.get('total_actions', 0)

                self.update_status(f"تم تعلم النمط ({actions_count} إجراء، ثقة: {confidence:.1%})", "success")
                self.pattern_status.config(text=f"{self.icons['success']} النمط: متعلم ({confidence:.1%})")

                # تفعيل أزرار التنفيذ والحفظ
                self.btn_execute.config(state=tk.NORMAL)
                self.btn_save_pattern.config(state=tk.NORMAL)
            else:
                self.update_status("لم يتم تعلم أي نمط", "warning")
                self.pattern_status.config(text=f"{self.icons['warning']} النمط: فشل التعلم")

            self.is_learning = False

            # تحديث الأزرار
            self.btn_start_learning.config(state=tk.NORMAL)
            self.btn_stop_learning.config(state=tk.DISABLED)

        except Exception as e:
            self.update_status(f"خطأ في إيقاف التعلم: {str(e)}", "error")
            self.is_learning = False
            self.btn_start_learning.config(state=tk.NORMAL)
            self.btn_stop_learning.config(state=tk.DISABLED)