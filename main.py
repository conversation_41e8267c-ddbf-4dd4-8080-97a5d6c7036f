# -*- coding: utf-8 -*-
"""
الملف الرئيسي لبرنامج تعبئة البيانات التلقائي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from gui.main_window import MainWindow
    from utils.logger import logger
    from config import UI_CONFIG
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements.txt")
    sys.exit(1)


def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    required_modules = [
        'selenium',
        'pandas',
        'openpyxl',
        'PIL',
        'pyautogui',
        'cv2',
        'webdriver_manager'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("المكتبات التالية غير مثبتة:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nلتثبيت المتطلبات، استخدم الأمر:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    from config import DATA_DIR, SESSIONS_DIR, LOGS_DIR
    
    directories = [DATA_DIR, SESSIONS_DIR, LOGS_DIR]
    
    for directory in directories:
        try:
            directory.mkdir(exist_ok=True)
            logger.info(f"تم إنشاء المجلد: {directory}")
        except Exception as e:
            logger.error(f"خطأ في إنشاء المجلد {directory}: {e}")
            return False
    
    return True


def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("برنامج تعبئة البيانات التلقائي")
    print("=" * 50)
    
    # التحقق من المتطلبات
    print("جاري التحقق من المتطلبات...")
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # إعداد المجلدات
    print("جاري إعداد المجلدات...")
    if not setup_directories():
        print("خطأ في إعداد المجلدات")
        input("اضغط Enter للخروج...")
        return
    
    try:
        # بدء تشغيل البرنامج
        print("جاري تشغيل البرنامج...")
        logger.info("بدء تشغيل البرنامج")
        
        app = MainWindow()
        app.run()
        
        logger.info("تم إغلاق البرنامج بنجاح")
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
        logger.info("تم إيقاف البرنامج بواسطة المستخدم")
        
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        logger.error(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")


if __name__ == "__main__":
    main()
