#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع للبرنامج
"""

import subprocess
import sys
import os

def main():
    """تشغيل البرنامج مباشرة"""
    try:
        # التأكد من وجود Python
        subprocess.run([sys.executable, "--version"], check=True, capture_output=True)
        
        # تشغيل البرنامج الرئيسي
        subprocess.run([sys.executable, "main.py"], check=True)
        
    except subprocess.CalledProcessError:
        print("خطأ: Python غير مثبت أو غير متاح")
        input("اضغط Enter للخروج...")
    except FileNotFoundError:
        print("خطأ: لم يتم العثور على main.py")
        input("اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج")
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
