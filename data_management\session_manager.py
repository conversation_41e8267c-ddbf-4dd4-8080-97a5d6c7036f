# -*- coding: utf-8 -*-
"""
مدير الجلسات - لإدارة جلسات التسجيل والتشغيل
"""

import json
import os
from datetime import datetime
from pathlib import Path
from config import SESSIONS_DIR
from utils.logger import logger


class SessionManager:
    """فئة إدارة الجلسات"""
    
    def __init__(self):
        self.current_session = None
        self.sessions_list = []
        self.load_sessions_list()
    
    def load_sessions_list(self):
        """تحميل قائمة الجلسات المحفوظة"""
        try:
            self.sessions_list = []
            
            if not SESSIONS_DIR.exists():
                SESSIONS_DIR.mkdir(exist_ok=True)
                return
            
            for file_path in SESSIONS_DIR.glob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    session_info = {
                        "file_path": str(file_path),
                        "file_name": file_path.name,
                        "session_name": session_data.get("session_info", {}).get("name", file_path.stem),
                        "created_at": session_data.get("session_info", {}).get("created_at", ""),
                        "total_actions": session_data.get("session_info", {}).get("total_actions", 0),
                        "recording_number": session_data.get("session_info", {}).get("recording_number", 0)
                    }
                    
                    self.sessions_list.append(session_info)
                    
                except Exception as e:
                    logger.warning(f"خطأ في قراءة الجلسة {file_path}: {str(e)}")
            
            # ترتيب الجلسات حسب تاريخ الإنشاء
            self.sessions_list.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            
            logger.info(f"تم تحميل {len(self.sessions_list)} جلسة")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل قائمة الجلسات: {str(e)}")
    
    def get_sessions_list(self):
        """الحصول على قائمة الجلسات"""
        return self.sessions_list.copy()
    
    def get_session_by_name(self, session_name):
        """الحصول على جلسة بالاسم"""
        for session in self.sessions_list:
            if session["session_name"] == session_name:
                return session
        return None
    
    def load_session_data(self, session_file_path):
        """تحميل بيانات جلسة معينة"""
        try:
            with open(session_file_path, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            logger.info(f"تم تحميل بيانات الجلسة: {session_file_path}")
            return session_data
            
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الجلسة: {str(e)}")
            return None
    
    def delete_session(self, session_file_path):
        """حذف جلسة"""
        try:
            file_path = Path(session_file_path)
            if file_path.exists():
                file_path.unlink()
                self.load_sessions_list()  # إعادة تحميل القائمة
                logger.info(f"تم حذف الجلسة: {session_file_path}")
                return True
            else:
                logger.warning(f"الجلسة غير موجودة: {session_file_path}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في حذف الجلسة: {str(e)}")
            return False
    
    def create_new_session_name(self, base_name="session"):
        """إنشاء اسم جلسة جديد"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{base_name}_{timestamp}"
    
    def get_session_statistics(self):
        """الحصول على إحصائيات الجلسات"""
        if not self.sessions_list:
            return {
                "total_sessions": 0,
                "total_actions": 0,
                "latest_session": None,
                "oldest_session": None
            }
        
        total_actions = sum(session.get("total_actions", 0) for session in self.sessions_list)
        
        # ترتيب الجلسات حسب التاريخ
        sorted_sessions = sorted(
            [s for s in self.sessions_list if s.get("created_at")],
            key=lambda x: x["created_at"]
        )
        
        return {
            "total_sessions": len(self.sessions_list),
            "total_actions": total_actions,
            "latest_session": sorted_sessions[-1] if sorted_sessions else None,
            "oldest_session": sorted_sessions[0] if sorted_sessions else None
        }
    
    def export_session(self, session_file_path, export_path):
        """تصدير جلسة إلى مكان آخر"""
        try:
            import shutil
            shutil.copy2(session_file_path, export_path)
            logger.info(f"تم تصدير الجلسة إلى: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الجلسة: {str(e)}")
            return False
    
    def import_session(self, import_path):
        """استيراد جلسة من مكان آخر"""
        try:
            import shutil
            import_file = Path(import_path)
            
            if not import_file.exists():
                logger.error(f"الملف غير موجود: {import_path}")
                return False
            
            # التحقق من صحة الملف
            with open(import_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            if "actions" not in session_data:
                logger.error("ملف الجلسة غير صحيح")
                return False
            
            # نسخ الملف إلى مجلد الجلسات
            destination = SESSIONS_DIR / import_file.name
            shutil.copy2(import_file, destination)
            
            # إعادة تحميل قائمة الجلسات
            self.load_sessions_list()
            
            logger.info(f"تم استيراد الجلسة: {import_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استيراد الجلسة: {str(e)}")
            return False
    
    def validate_session_file(self, file_path):
        """التحقق من صحة ملف الجلسة"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            # التحقق من وجود الحقول المطلوبة
            required_fields = ["actions"]
            for field in required_fields:
                if field not in session_data:
                    return False, f"الحقل المطلوب غير موجود: {field}"
            
            # التحقق من صحة الإجراءات
            actions = session_data["actions"]
            if not isinstance(actions, list):
                return False, "الإجراءات يجب أن تكون قائمة"
            
            for i, action in enumerate(actions):
                if not isinstance(action, dict):
                    return False, f"الإجراء {i} يجب أن يكون كائن"
                
                if "type" not in action:
                    return False, f"الإجراء {i} يفتقر لحقل النوع"
            
            return True, "ملف الجلسة صحيح"
            
        except json.JSONDecodeError:
            return False, "ملف JSON غير صحيح"
        except Exception as e:
            return False, f"خطأ في التحقق: {str(e)}"
