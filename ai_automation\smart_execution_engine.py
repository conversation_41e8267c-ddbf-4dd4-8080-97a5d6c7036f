# -*- coding: utf-8 -*-
"""
محرك التنفيذ الذكي
ينفذ الأنماط المتعلمة بذكاء ويتكيف مع التغييرات
"""

import time
import json
from typing import List, Dict, Optional, Any
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException
from ai_automation.smart_element_detector import SmartElementDetector
from utils.logger import logger

class SmartExecutionEngine:
    """محرك التنفيذ الذكي"""
    
    def __init__(self, web_driver):
        self.web_driver = web_driver
        self.element_detector = SmartElementDetector(web_driver)
        
        # إعدادات التنفيذ
        self.execution_delay = 1.0  # تأخير بين الإجراءات
        self.retry_attempts = 3
        self.timeout = 10
        
        # حالة التنفيذ
        self.is_executing = False
        self.current_pattern = None
        self.execution_results = []
        self.failed_actions = []
        
        # إحصائيات
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        
    def execute_pattern(self, pattern: Dict, data_rows: List[Dict], 
                       progress_callback: Optional[callable] = None) -> Dict:
        """تنفيذ النمط على مجموعة من البيانات"""
        try:
            if self.is_executing:
                logger.warning("التنفيذ قيد التشغيل بالفعل")
                return {'success': False, 'error': 'التنفيذ قيد التشغيل'}
            
            self.is_executing = True
            self.current_pattern = pattern
            self.execution_results = []
            self.failed_actions = []
            
            logger.info(f"بدء تنفيذ النمط على {len(data_rows)} صف من البيانات")
            
            # التحقق من صحة النمط
            if not self._validate_pattern(pattern):
                return {'success': False, 'error': 'النمط غير صحيح'}
            
            # تحليل هيكل الصفحة الحالي
            current_structure = self.element_detector.analyze_page_structure()
            
            # تنفيذ النمط لكل صف
            for i, data_row in enumerate(data_rows):
                if not self.is_executing:  # إذا تم إيقاف التنفيذ
                    break
                
                logger.info(f"تنفيذ الصف {i + 1}/{len(data_rows)}")
                
                # تنفيذ النمط للصف الحالي
                row_result = self._execute_single_row(pattern, data_row, current_structure)
                self.execution_results.append(row_result)
                
                # تحديث التقدم
                if progress_callback:
                    progress = (i + 1) / len(data_rows) * 100
                    progress_callback(progress, row_result)
                
                # تأخير بين الصفوف
                if i < len(data_rows) - 1:
                    time.sleep(self.execution_delay)
            
            # تجميع النتائج
            results_summary = self._summarize_results()
            
            self.is_executing = False
            logger.info(f"انتهى التنفيذ. نجح: {results_summary['successful']}, فشل: {results_summary['failed']}")
            
            return {
                'success': True,
                'results': self.execution_results,
                'summary': results_summary,
                'failed_actions': self.failed_actions
            }
            
        except Exception as e:
            self.is_executing = False
            logger.error(f"خطأ في تنفيذ النمط: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _validate_pattern(self, pattern: Dict) -> bool:
        """التحقق من صحة النمط"""
        try:
            required_keys = ['form_fields', 'navigation_pattern']
            
            for key in required_keys:
                if key not in pattern:
                    logger.error(f"النمط لا يحتوي على: {key}")
                    return False
            
            if not pattern['form_fields']:
                logger.error("النمط لا يحتوي على حقول نموذج")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من النمط: {str(e)}")
            return False
    
    def _execute_single_row(self, pattern: Dict, data_row: Dict, page_structure: Dict) -> Dict:
        """تنفيذ النمط لصف واحد من البيانات"""
        try:
            row_result = {
                'success': False,
                'data_row': data_row,
                'executed_actions': [],
                'failed_actions': [],
                'execution_time': time.time()
            }
            
            # تنفيذ حقول النموذج
            form_success = self._execute_form_fields(pattern['form_fields'], data_row, row_result)
            
            if form_success:
                # تنفيذ إجراءات التنقل (مثل الضغط على زر الإرسال)
                nav_success = self._execute_navigation(pattern['navigation_pattern'], row_result)
                row_result['success'] = nav_success
            
            row_result['execution_time'] = time.time() - row_result['execution_time']
            
            if row_result['success']:
                self.successful_executions += 1
            else:
                self.failed_executions += 1
            
            self.total_executions += 1
            
            return row_result
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الصف: {str(e)}")
            return {
                'success': False,
                'data_row': data_row,
                'error': str(e),
                'execution_time': 0
            }
    
    def _execute_form_fields(self, form_fields: List[Dict], data_row: Dict, row_result: Dict) -> bool:
        """تنفيذ تعبئة حقول النموذج"""
        try:
            for field in form_fields:
                field_name = field.get('name', '')
                selector = field.get('selector', '')
                element_type = field.get('element_type', 'text_input')
                
                # العثور على القيمة المناسبة من البيانات
                field_value = self._find_matching_data_value(field, data_row)
                
                if field_value is None:
                    logger.warning(f"لم يتم العثور على قيمة للحقل: {field_name}")
                    continue
                
                # العثور على العنصر وتعبئته
                element = self._find_element_smart(selector, field)
                
                if element:
                    success = self._fill_element(element, field_value, element_type)
                    
                    action_result = {
                        'field_name': field_name,
                        'selector': selector,
                        'value': field_value,
                        'success': success
                    }
                    
                    if success:
                        row_result['executed_actions'].append(action_result)
                        logger.debug(f"تم تعبئة الحقل: {field_name} = {field_value}")
                    else:
                        row_result['failed_actions'].append(action_result)
                        logger.warning(f"فشل في تعبئة الحقل: {field_name}")
                else:
                    logger.warning(f"لم يتم العثور على العنصر: {selector}")
                    row_result['failed_actions'].append({
                        'field_name': field_name,
                        'selector': selector,
                        'error': 'عنصر غير موجود'
                    })
                
                # تأخير قصير بين الحقول
                time.sleep(0.5)
            
            return len(row_result['failed_actions']) == 0
            
        except Exception as e:
            logger.error(f"خطأ في تعبئة حقول النموذج: {str(e)}")
            return False
    
    def _execute_navigation(self, navigation_pattern: List[Dict], row_result: Dict) -> bool:
        """تنفيذ إجراءات التنقل"""
        try:
            for nav_step in navigation_pattern:
                action = nav_step.get('action', '')
                selector = nav_step.get('selector', '')
                
                if action == 'click':
                    element = self._find_element_smart(selector, nav_step)
                    
                    if element:
                        success = self._click_element(element)
                        
                        nav_result = {
                            'action': action,
                            'selector': selector,
                            'success': success
                        }
                        
                        if success:
                            row_result['executed_actions'].append(nav_result)
                            logger.debug(f"تم النقر على: {selector}")
                            
                            # انتظار قصير بعد النقر
                            time.sleep(1.0)
                        else:
                            row_result['failed_actions'].append(nav_result)
                            logger.warning(f"فشل في النقر على: {selector}")
                            return False
                    else:
                        logger.warning(f"لم يتم العثور على عنصر التنقل: {selector}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ التنقل: {str(e)}")
            return False
    
    def _find_matching_data_value(self, field: Dict, data_row: Dict) -> Optional[str]:
        """العثور على القيمة المناسبة من البيانات للحقل"""
        try:
            field_name = field.get('name', '')
            placeholder = field.get('placeholder', '')
            sample_value = field.get('sample_value', '')
            
            # البحث المباشر بالاسم
            if field_name and field_name in data_row:
                return str(data_row[field_name])
            
            # البحث بالـ placeholder
            if placeholder:
                for key, value in data_row.items():
                    if placeholder.lower() in key.lower():
                        return str(value)
            
            # البحث الذكي بناءً على نوع البيانات
            field_type = self._detect_field_type(field)
            
            for key, value in data_row.items():
                if self._is_data_type_match(str(value), field_type):
                    return str(value)
            
            # استخدام أول قيمة متاحة كحل أخير
            if data_row:
                return str(list(data_row.values())[0])
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في العثور على قيمة البيانات: {str(e)}")
            return None
    
    def _detect_field_type(self, field: Dict) -> str:
        """تحديد نوع الحقل"""
        element_type = field.get('element_type', '')
        name = field.get('name', '').lower()
        placeholder = field.get('placeholder', '').lower()
        
        # تحديد النوع بناءً على الاسم أو placeholder
        if any(keyword in name + placeholder for keyword in ['email', 'بريد']):
            return 'email'
        elif any(keyword in name + placeholder for keyword in ['phone', 'tel', 'هاتف', 'جوال']):
            return 'phone'
        elif any(keyword in name + placeholder for keyword in ['name', 'اسم']):
            return 'name'
        elif any(keyword in name + placeholder for keyword in ['age', 'عمر']):
            return 'number'
        else:
            return 'text'
    
    def _is_data_type_match(self, value: str, field_type: str) -> bool:
        """التحقق من تطابق نوع البيانات"""
        import re
        
        if field_type == 'email':
            return re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', value) is not None
        elif field_type == 'phone':
            return re.match(r'^\+?\d{10,15}$', value.replace(' ', '').replace('-', '')) is not None
        elif field_type == 'number':
            return value.isdigit()
        elif field_type == 'name':
            return re.match(r'^[a-zA-Zأ-ي\s]+$', value) is not None
        else:
            return True  # نص عام
    
    def _find_element_smart(self, selector: str, element_info: Dict) -> Optional[Any]:
        """العثور على العنصر بطريقة ذكية"""
        try:
            # محاولة الـ selector الأساسي أولاً
            if selector:
                try:
                    element = WebDriverWait(self.web_driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if element.is_displayed():
                        return element
                except (TimeoutException, NoSuchElementException):
                    pass
            
            # محاولة طرق بديلة
            alternative_selectors = self._generate_alternative_selectors(element_info)
            
            for by_method, selector_value in alternative_selectors:
                if selector_value:
                    try:
                        element = WebDriverWait(self.web_driver, 2).until(
                            EC.presence_of_element_located((by_method, selector_value))
                        )
                        if element.is_displayed():
                            return element
                    except (TimeoutException, NoSuchElementException):
                        continue
            
            logger.warning(f"لم يتم العثور على العنصر: {selector}")
            return None
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن العنصر: {str(e)}")
            return None
    
    def _generate_alternative_selectors(self, element_info: Dict) -> List[tuple]:
        """إنشاء selectors بديلة للعنصر"""
        alternatives = []
        
        # استخدام name
        if 'name' in element_info:
            alternatives.append((By.NAME, element_info['name']))
        
        # استخدام placeholder
        if 'placeholder' in element_info:
            alternatives.append((By.CSS_SELECTOR, f"[placeholder='{element_info['placeholder']}']"))
        
        # استخدام type
        element_type = element_info.get('element_type', '')
        if element_type:
            alternatives.append((By.CSS_SELECTOR, f"input[type='{element_type}']"))
        
        return alternatives
    
    def _fill_element(self, element, value: str, element_type: str) -> bool:
        """تعبئة العنصر بالقيمة"""
        try:
            # التأكد من أن العنصر قابل للتفاعل
            WebDriverWait(self.web_driver, 5).until(
                EC.element_to_be_clickable(element)
            )
            
            if element_type == 'select':
                # قائمة منسدلة
                select = Select(element)
                try:
                    select.select_by_visible_text(value)
                except:
                    try:
                        select.select_by_value(value)
                    except:
                        select.select_by_index(0)  # اختيار أول خيار
            
            elif element_type in ['text_input', 'textarea', 'input']:
                # حقل نص
                element.clear()
                element.send_keys(value)
            
            elif element_type == 'checkbox':
                # مربع اختيار
                if value.lower() in ['true', '1', 'yes', 'نعم']:
                    if not element.is_selected():
                        element.click()
                else:
                    if element.is_selected():
                        element.click()
            
            else:
                # افتراضي: حقل نص
                element.clear()
                element.send_keys(value)
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تعبئة العنصر: {str(e)}")
            return False
    
    def _click_element(self, element) -> bool:
        """النقر على العنصر"""
        try:
            # التأكد من أن العنصر قابل للنقر
            WebDriverWait(self.web_driver, 5).until(
                EC.element_to_be_clickable(element)
            )
            
            # محاولة النقر العادي أولاً
            try:
                element.click()
                return True
            except ElementNotInteractableException:
                # استخدام JavaScript كبديل
                self.web_driver.execute_script("arguments[0].click();", element)
                return True
            
        except Exception as e:
            logger.error(f"خطأ في النقر على العنصر: {str(e)}")
            return False
    
    def _summarize_results(self) -> Dict:
        """تلخيص نتائج التنفيذ"""
        try:
            successful = sum(1 for result in self.execution_results if result.get('success', False))
            failed = len(self.execution_results) - successful
            
            total_time = sum(result.get('execution_time', 0) for result in self.execution_results)
            avg_time = total_time / len(self.execution_results) if self.execution_results else 0
            
            return {
                'total_rows': len(self.execution_results),
                'successful': successful,
                'failed': failed,
                'success_rate': (successful / len(self.execution_results) * 100) if self.execution_results else 0,
                'total_execution_time': total_time,
                'average_time_per_row': avg_time
            }
            
        except Exception as e:
            logger.error(f"خطأ في تلخيص النتائج: {str(e)}")
            return {}
    
    def stop_execution(self):
        """إيقاف التنفيذ"""
        self.is_executing = False
        logger.info("تم إيقاف التنفيذ")
    
    def get_execution_stats(self) -> Dict:
        """الحصول على إحصائيات التنفيذ"""
        return {
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'failed_executions': self.failed_executions,
            'success_rate': (self.successful_executions / self.total_executions * 100) if self.total_executions > 0 else 0
        }
